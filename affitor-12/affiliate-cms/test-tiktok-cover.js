/**
 * Test script for TikTok video detail with cover image processing
 *
 * This script tests the complete flow:
 * 1. Fetch TikTok video details
 * 2. Download cover image
 * 3. Upload to AWS S3
 * 4. Get public link
 *
 * Usage: node test-tiktok-cover.js [videoId]
 * Example: node test-tiktok-cover.js 6845220806658624773
 */

const axios = require('axios');

// Configuration
const BASE_URL = process.env.BASE_URL || 'http://prod-alb-521497661.us-east-1.elb.amazonaws.com';
const TEST_VIDEO_ID = process.argv[2] || '6845220806658624773'; // Default test video ID

async function testTikTokCoverProcessing() {
  console.log('🚀 Testing TikTok Video Detail with Cover Processing');
  console.log('================================================');
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Video ID: ${TEST_VIDEO_ID}`);
  console.log('');

  try {
    // Test the endpoint
    console.log('📡 Making request to TikTok detail endpoint...');
    const startTime = Date.now();

    const response = await axios.get(
      `${BASE_URL}/api/social-listenings/tiktok-detail-cover/${TEST_VIDEO_ID}`,
      {
        timeout: 60000, // 60 seconds timeout
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`✅ Request completed in ${duration}ms`);
    console.log('');

    // Check response
    if (response.status === 200) {
      console.log('🎉 SUCCESS! Response received:');
      console.log('Status:', response.status);
      console.log('');

      const data = response.data;

      if (data.success) {
        console.log('✅ Operation successful!');
        console.log('');
        console.log('📊 Results:');
        console.log('- Video ID:', data.data.videoId);
        console.log('- Original Thumbnail:', data.data.originalThumbnail);
        console.log('- S3 Public URL:', data.data.s3Url);
        console.log('- S3 File Key:', data.data.s3FileKey);
        console.log('- S3 Bucket:', data.data.s3Bucket);
        console.log('- File Name:', data.data.fileName);
        console.log('');

        // Test the public link
        console.log('🔗 Testing S3 public link...');
        try {
          const linkResponse = await axios.head(data.data.s3Url, {
            timeout: 10000,
          });
          console.log('✅ Public link is accessible!');
          console.log('- Status:', linkResponse.status);
          console.log('- Content Type:', linkResponse.headers['content-type']);
          console.log('- Content Length:', linkResponse.headers['content-length']);
        } catch (linkError) {
          console.log('❌ Public link test failed:', linkError.message);
        }
      } else {
        console.log('❌ Operation failed:');
        console.log('Error:', data.error);
      }
    } else {
      console.log('❌ Unexpected response status:', response.status);
    }
  } catch (error) {
    console.log('❌ Test failed with error:');

    if (error.response) {
      console.log('- Status:', error.response.status);
      console.log('- Status Text:', error.response.statusText);
      console.log('- Response Data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('- No response received');
      console.log('- Error:', error.message);
    } else {
      console.log('- Error:', error.message);
    }
  }

  console.log('');
  console.log('🏁 Test completed');
}

// Additional test for direct API calls
async function testDirectTikTokAPI() {
  console.log('');
  console.log('🔧 Testing direct TikTok API call...');

  try {
    const response = await axios.get(
      `https://tiktok-api23.p.rapidapi.com/api/post/detail?videoId=${TEST_VIDEO_ID}`,
      {
        headers: {
          'x-rapidapi-host': 'tiktok-api23.p.rapidapi.com',
          'x-rapidapi-key':
            process.env.RAPIDAPI_KEY || '**************************************************',
        },
        timeout: 10000,
      }
    );

    if (response.data?.itemInfo?.itemStruct?.video?.originCover) {
      console.log('✅ Direct TikTok API call successful!');
      console.log('- Thumbnail URL:', response.data.itemInfo.itemStruct.video.originCover);
    } else {
      console.log('❌ Direct TikTok API call failed - no thumbnail found');
    }
  } catch (error) {
    console.log('❌ Direct TikTok API call failed:', error.message);
  }
}

// Run the tests
async function runTests() {
  await testTikTokCoverProcessing();
  // await testDirectTikTokAPI();
}

// Check if AWS S3 credentials are configured
function checkConfiguration() {
  console.log('🔍 Checking AWS S3 configuration...');

  const requiredEnvVars = ['AWS_ACCESS_KEY_ID', 'AWS_ACCESS_SECRET', 'AWS_BUCKET', 'AWS_REGION'];

  const missing = requiredEnvVars.filter((varName) => !process.env[varName]);

  if (missing.length > 0) {
    console.log('⚠️  Warning: Missing AWS S3 configuration:');
    missing.forEach((varName) => console.log(`   - ${varName}`));
    console.log('   The test may fail if AWS S3 credentials are not properly configured.');
  } else {
    console.log('✅ AWS S3 configuration appears to be set');
    console.log('- AWS Region:', process.env.AWS_REGION);
    console.log('- AWS Bucket:', process.env.AWS_BUCKET);
  }
  console.log('');
}

// Main execution
if (require.main === module) {
  checkConfiguration();
  runTests().catch(console.error);
}

module.exports = {
  testTikTokCoverProcessing,
  testDirectTikTokAPI,
};
