{"name": "affiliate-cms", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "seed:example": "node ./scripts/seed.js", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\""}, "dependencies": {"@aws-sdk/client-s3": "^3.842.0", "@langchain/anthropic": "^0.3.17", "@langchain/core": "^0.3.44", "@langchain/google-genai": "^0.2.3", "@langchain/openai": "^0.5.5", "@strapi/plugin-cloud": "5.11.2", "@strapi/plugin-users-permissions": "5.11.2", "@strapi/provider-upload-aws-s3": "^4.25.6", "@strapi/strapi": "5.11.2", "axios": "^1.10.0", "firebase-admin": "^13.2.0", "fs-extra": "^10.0.0", "langchain": "^0.3.21", "mime-types": "^2.1.27", "pg": "8.8.0", "puppeteer": "^24.9.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "sharp": "^0.34.2", "strapi-import-export": "^0.0.1", "stripe": "^18.1.0", "styled-components": "^6.0.0", "youtubei.js": "^13.4.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "prettier": "^3.0.3", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************"}, "packageManager": "yarn@1.22.21+sha1.1959a18351b811cdeedbd484a8f86c3cc3bbaf72"}