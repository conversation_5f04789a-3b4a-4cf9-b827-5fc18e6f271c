variable "name" {
  description = "The name prefix to use for resources in the VPC, typically includes the environment (e.g., my-vpc-dev)."
  type        = string
  default     = "affiliate-vpc"
}

variable "cidr" {
  description = "The CIDR block for the VPC."
  type        = string
  default     = "10.0.0.0/16"
}

variable "azs" {
  description = "A list of availability zones in which to create subnets."
  type        = list(string)
  default     = ["us-east-1a", "us-east-1b", "us-east-1c"]
}

variable "public_subnet_cidrs" {
  description = "A list of CIDR blocks for the public subnets."
  type        = list(string)
  default     = ["********/24", "********/24", "********/24"]
}

variable "private_subnet_cidrs" {
  description = "A list of CIDR blocks for the private subnets."
  type        = list(string)
  default     = ["********/24", "********/24", "********/24"]
}

variable "tags" {
  description = "A map of tags to apply to all resources."
  type        = map(string)
  default     = {
    "ManagedBy" = "Terraform"
  }
}

variable "environment" {
  description = "The environment in which the VPC is being created (e.g., dev, prod)."
  type        = string
  default     = "dev"
}
