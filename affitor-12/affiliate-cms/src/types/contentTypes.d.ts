// Bank details component type
export interface BankTransferDetails {
  first_name?: string;
  last_name?: string;
  business_name?: string;
  country?: string;
  city?: string;
  state?: string;
  address?: string;
  zip_code?: string;
  account_number?: string;
  swift_code?: string;
}

// Extend Strapi's user model
declare module '@strapi/strapi' {
  interface Schemas {
    'plugin::users-permissions.user': {
      attributes: {
        // Base attributes
        id: number;
        username: string;
        email: string;
        provider: string;
        password: string;
        resetPasswordToken: string;
        confirmationToken: string;
        confirmed: boolean;
        blocked: boolean;
        role: any;

        // Custom existing attributes
        stripe_customer_id: string;
        user_tracking_request: any;
        subscription_tier: any;

        // New profile fields
        first_name: string;
        last_name: string;
        address: string;
        apt: string;
        city: string;
        country: string;
        zip_code: string;
        state: string;
        paypal_email: string;
        bank_transfer: BankTransferDetails;

        // Timestamps
        createdAt: string;
        updatedAt: string;
      };
    };
  }
}

// Profile update request body type
export interface ProfileUpdateBody {
  first_name?: string;
  last_name?: string;
  address?: string;
  apt?: string;
  city?: string;
  country?: string;
  zip_code?: string;
  state?: string;
  paypal_email?: string;
  bank_transfer?: BankTransferDetails;
}
