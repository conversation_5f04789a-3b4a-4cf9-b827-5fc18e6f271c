/**
 * CloudWatch-friendly logging utilities
 * 
 * This utility provides structured logging that works well with CloudWatch,
 * avoiding multi-line JSON output that gets split across multiple log entries.
 */

interface LogContext {
  [key: string]: any;
}

interface QueryLogContext {
  userId?: string | number;
  entityType?: string;
  operation?: string;
  filters?: Record<string, any>;
  pagination?: Record<string, any>;
  sort?: string;
  resultCount?: number;
  duration?: number;
}

class CloudWatchLogger {
  private formatContext(context: LogContext): string {
    // Remove null/undefined values and format for single-line output
    const cleanContext = Object.entries(context)
      .filter(([_, value]) => value !== null && value !== undefined)
      .reduce((acc, [key, value]) => {
        // Convert complex objects to simple representations
        if (typeof value === 'object' && value !== null) {
          if (Array.isArray(value)) {
            acc[key] = `[${value.length} items]`;
          } else if (value instanceof Date) {
            acc[key] = value.toISOString();
          } else {
            // For objects, show key count or specific properties
            const keys = Object.keys(value);
            acc[key] = keys.length > 0 ? `{${keys.join(',')}}` : '{}';
          }
        } else {
          acc[key] = value;
        }
        return acc;
      }, {} as LogContext);

    return JSON.stringify(cleanContext);
  }

  /**
   * Log API request information
   */
  logRequest(service: string, method: string, context: LogContext = {}) {
    const logContext = {
      service,
      method,
      timestamp: new Date().toISOString(),
      ...context
    };
    strapi.log.info(`[${service}] ${method} - ${this.formatContext(logContext)}`);
  }

  /**
   * Log query operations with performance metrics
   */
  logQuery(service: string, context: QueryLogContext) {
    const logContext = {
      service,
      operation: context.operation || 'query',
      userId: context.userId,
      entityType: context.entityType,
      hasFilters: context.filters ? Object.keys(context.filters).length > 0 : false,
      filterCount: context.filters ? Object.keys(context.filters).length : 0,
      resultCount: context.resultCount,
      duration: context.duration,
      timestamp: new Date().toISOString()
    };
    strapi.log.info(`[${service}] Query - ${this.formatContext(logContext)}`);
  }

  /**
   * Log successful operations
   */
  logSuccess(service: string, operation: string, context: LogContext = {}) {
    const logContext = {
      service,
      operation,
      status: 'success',
      timestamp: new Date().toISOString(),
      ...context
    };
    strapi.log.info(`[${service}] Success - ${this.formatContext(logContext)}`);
  }

  /**
   * Log errors with context
   */
  logError(service: string, operation: string, error: Error, context: LogContext = {}) {
    const logContext = {
      service,
      operation,
      status: 'error',
      errorMessage: error.message,
      errorName: error.name,
      timestamp: new Date().toISOString(),
      ...context
    };
    strapi.log.error(`[${service}] Error - ${this.formatContext(logContext)}`);
  }

  /**
   * Log warnings
   */
  logWarning(service: string, message: string, context: LogContext = {}) {
    const logContext = {
      service,
      warning: message,
      timestamp: new Date().toISOString(),
      ...context
    };
    strapi.log.warn(`[${service}] Warning - ${this.formatContext(logContext)}`);
  }

  /**
   * Log performance metrics
   */
  logPerformance(service: string, operation: string, duration: number, context: LogContext = {}) {
    const logContext = {
      service,
      operation,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString(),
      ...context
    };
    strapi.log.info(`[${service}] Performance - ${this.formatContext(logContext)}`);
  }

  /**
   * Log business logic events
   */
  logBusinessEvent(service: string, event: string, context: LogContext = {}) {
    const logContext = {
      service,
      event,
      timestamp: new Date().toISOString(),
      ...context
    };
    strapi.log.info(`[${service}] Event - ${this.formatContext(logContext)}`);
  }
}

// Export singleton instance
export const cloudWatchLogger = new CloudWatchLogger();

// Export helper functions for common use cases
export const logApiRequest = (service: string, method: string, context?: LogContext) => 
  cloudWatchLogger.logRequest(service, method, context);

export const logQuery = (service: string, context: QueryLogContext) => 
  cloudWatchLogger.logQuery(service, context);

export const logSuccess = (service: string, operation: string, context?: LogContext) => 
  cloudWatchLogger.logSuccess(service, operation, context);

export const logError = (service: string, operation: string, error: Error, context?: LogContext) => 
  cloudWatchLogger.logError(service, operation, error, context);

export const logWarning = (service: string, message: string, context?: LogContext) => 
  cloudWatchLogger.logWarning(service, message, context);

export const logPerformance = (service: string, operation: string, duration: number, context?: LogContext) => 
  cloudWatchLogger.logPerformance(service, operation, duration, context);

export const logBusinessEvent = (service: string, event: string, context?: LogContext) => 
  cloudWatchLogger.logBusinessEvent(service, event, context);
