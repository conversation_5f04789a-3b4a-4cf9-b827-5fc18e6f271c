/**
 * Custom API routes for user-tracking-request
 * These routes provide additional functionality beyond the standard CRUD operations
 */

export default {
  routes: [
    {
      method: 'GET',
      path: '/user-tracking-request/my-stats',
      handler: 'user-tracking-request.getUserStats',
      config: {
        auth: {
          scope: ['authenticated'],
        },
      },
    },
    {
      method: 'POST',
      path: '/user-tracking-request/manual-daily-reset',
      handler: 'user-tracking-request.manualDailyReset',
      config: {
        auth: {
          scope: ['authenticated'],
        },
      },
    },
    {
      method: 'POST',
      path: '/user-tracking-request/update-basic-daily-limits',
      handler: 'user-tracking-request.updateBasicDailyLimits',
      config: {
        auth: false, // No authentication required
      },
    },
  ],
};