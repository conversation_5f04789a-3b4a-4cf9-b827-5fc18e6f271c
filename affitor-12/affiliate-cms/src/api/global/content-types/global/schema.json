{"kind": "singleType", "collectionName": "globals", "info": {"singularName": "global", "pluralName": "globals", "displayName": "Global", "description": "Define global settings"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"siteName": {"type": "string", "required": true}, "favicon": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos"]}, "siteDescription": {"type": "text", "required": true}, "ignore_tiktok_des": {"type": "text"}, "defaultSeo": {"type": "component", "repeatable": false, "component": "shared.seo"}, "aiModel": {"type": "enumeration", "enum": ["gpt-4o", "claude-3-sonnet-3.7", "gemini-1.5-pro-002", "gemini-2.0-flash-lite", "gemini-1.5-flash", "gemini-2.0-flash", "n8n-gpt", "n8n-gemini"]}, "default_free_requests": {"type": "integer", "default": 100, "description": "Default number of free requests for new users"}, "refresh_social_after": {"type": "integer", "default": 300, "description": "Time in seconds to wait before refreshing empty social data results"}, "refresh_youtube_interval": {"type": "integer", "default": 600, "description": "Time in seconds to wait before refreshing YouTube data"}, "refresh_tiktok_interval": {"type": "integer", "default": 300, "description": "Time in seconds to wait before refreshing TikTok data"}, "airtable_sync_schedule": {"type": "enumeration", "enum": ["Daily (midnight)", "Every minute", "Every 5 minutes", "Every 10 minutes", "Every 30 minutes", "Hourly"], "default": "Daily (midnight)", "description": "How often to sync with Airtable"}, "tiktok_thumbnail_update_schedule": {"type": "enumeration", "enum": ["Daily (midnight)", "Every 5 minutes", "Every 2 hours", "Every 4 hours", "Every 3 days", "Weekly", "Every 2 weeks", "Monthly"], "default": "Every 3 days", "description": "How often to update TikTok video thumbnails"}, "defaultPrompt": {"type": "text", "description": "Default prompt template for AI Script generation"}, "summaryPrompt": {"type": "text", "description": "The summary prompt template for AI Script generation"}, "airtable_base_id": {"type": "string", "description": "The ID of the Airtable base (e.g. apppP1zjvDOwdNqvi)"}, "airtable_table_id": {"type": "string", "description": "The ID of the submissions table in Airtable (e.g. tbldvvz0ImTQWYDkG)"}, "default_user_ref_rate": {"type": "decimal", "description": "Default user referral rate for user's submissions"}, "fetch_ads_schedule": {"type": "enumeration", "enum": ["Daily (midnight)", "Every 2 minutes", "Every 5 minutes", "Every 10 minutes", "Every 15 minutes", "Every 30 minutes", "Every hour", "Every 4 hours", "Every 8 hours", "Every 12 hours", "Weekly"], "default": "Daily (midnight)", "description": "How often to fetch new trending ads for affiliates"}, "resolved_affiliates": {"type": "json", "description": "List of affiliate IDs that have already had their ads fetched", "default": []}, "slack_webhook_url": {"type": "string", "description": "Webhook URL for Slack notifications"}}}