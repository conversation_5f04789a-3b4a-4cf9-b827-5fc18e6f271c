export default {
  routes: [
    {
      method: 'GET',
      path: '/transactions/history',
      handler: 'transaction.getUserTransactions',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/transactions/process-payment',
      handler: 'transaction.processPayment',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/transactions/:id/invoice',
      handler: 'transaction.generateInvoice',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
