export interface IReferrerLink {
  id: number;
  documentId: string;
  name: string;
  url: string;
  short_link?: string;
  visitors: number;
  leads: number;
  conversions: number;
  // Additional view tracking fields (visitors is the primary field)
  direct_page_views?: number;
  referrer_link_views?: number;
  short_link_views?: number;
  referrer_sources?: Record<string, number>; // JSON object storing referrer information and counts
  // Relations
  user?: any; // User relation
  referrer?: any; // Referrer relation
  page?: {
    id: number;
    documentId: string;
    title: string;
    slug: string;
    status: 'draft' | 'published' | 'archived';
  }; // Page relation
  track_links?: any[]; // Track links relation
  referrals?: any[]; // Referrals relation
  // Timestamps
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

export interface ICreateReferrerLinkData {
  name: string;
  url: string;
  short_link?: string;
  page_id?: string; // For linking to a specific page
  user: number; // User ID
  referrer: number; // Referrer ID
}

export interface IUpdateReferrerLinkData {
  name?: string;
  url?: string;
  short_link?: string;
  page_id?: string;
  visitors?: number;
  leads?: number;
  conversions?: number;
  direct_page_views?: number;
  referrer_link_views?: number;
  short_link_views?: number;
  referrer_sources?: Record<string, number>;
}

export interface IReferrerLinkFilters {
  name?: string;
  url?: string;
  user?: number;
  referrer?: number;
  page?: number;
  createdAt?: {
    $gte?: string;
    $lte?: string;
  };
}

export interface IReferrerLinkPagination {
  page?: number;
  pageSize?: number;
  start?: number;
  limit?: number;
}

export interface IReferrerLinkResponse {
  data: IReferrerLink;
  meta?: any;
}

export interface IReferrerLinksResponse {
  data: IReferrerLink[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface IViewTrackingData {
  pageId: string;
  sourceType: 'direct' | 'referrer_link' | 'short_link';
  referrer?: string;
}

export interface IViewTrackingResponse {
  success: boolean;
  pageId: string;
  referrerLinkId?: number;
  sourceType: string;
  referrer?: string;
  viewCount: number;
}
