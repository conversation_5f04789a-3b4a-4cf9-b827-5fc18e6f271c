{"kind": "collectionType", "collectionName": "referrals", "info": {"singularName": "referral", "pluralName": "referrals", "displayName": "Referral", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"referral_status": {"type": "enumeration", "enum": ["cross", "lead", "conversion"]}, "total_paid": {"type": "decimal", "default": 0}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "inversedBy": "referral"}, "referrer": {"type": "relation", "relation": "manyToOne", "target": "api::referrer.referrer", "inversedBy": "referrals"}, "referrer_link": {"type": "relation", "relation": "manyToOne", "target": "api::referrer-link.referrer-link", "inversedBy": "referrals"}, "referral_activities": {"type": "relation", "relation": "oneToMany", "target": "api::referral-activity.referral-activity", "mappedBy": "referral"}, "referral_commissions": {"type": "relation", "relation": "oneToMany", "target": "api::referral-commission.referral-commission", "mappedBy": "referral"}}}