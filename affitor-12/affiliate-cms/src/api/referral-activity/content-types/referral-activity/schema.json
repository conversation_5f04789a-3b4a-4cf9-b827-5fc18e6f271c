{"kind": "collectionType", "collectionName": "referral_activities", "info": {"singularName": "referral-activity", "pluralName": "referral-activities", "displayName": "Referral Activity", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"referral": {"type": "relation", "relation": "manyToOne", "target": "api::referral.referral", "inversedBy": "referral_activities"}, "amount_paid": {"type": "decimal"}, "referral_status": {"type": "string"}, "description": {"type": "string"}}}