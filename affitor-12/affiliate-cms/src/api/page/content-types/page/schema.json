{"kind": "collectionType", "collectionName": "pages", "info": {"singularName": "page", "pluralName": "pages", "displayName": "User Page", "description": "User-generated pages with rich text content"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true, "maxLength": 255}, "slug": {"type": "uid", "targetField": "title", "required": true}, "content": {"type": "json", "required": false, "description": "Rich text content from Yoopta Editor stored as JSON blocks structure"}, "content_html": {"type": "text", "required": false, "description": "HTML representation of the content for SEO and fallback rendering"}, "content_plain": {"type": "text", "required": false, "description": "Plain text representation of the content for search and excerpts"}, "excerpt": {"type": "text", "maxLength": 500, "description": "Short description or excerpt of the page"}, "featured_image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "meta_title": {"type": "string", "maxLength": 60, "description": "SEO meta title"}, "meta_description": {"type": "text", "maxLength": 160, "description": "SEO meta description"}, "status": {"type": "enumeration", "enum": ["draft", "published", "archived"], "default": "draft"}, "view_count": {"type": "integer", "default": 0, "description": "Number of times the page has been viewed"}, "author": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "tags": {"type": "json", "description": "Array of tags for categorization"}, "referrer_link": {"type": "relation", "relation": "oneToOne", "target": "api::referrer-link.referrer-link", "mappedBy": "page"}, "last_edited_at": {"type": "datetime", "description": "Timestamp of last edit for auto-save tracking"}}}