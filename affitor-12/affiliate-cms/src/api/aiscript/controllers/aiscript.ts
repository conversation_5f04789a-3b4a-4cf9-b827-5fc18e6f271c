/**
 * aiscript controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::aiscript.aiscript', ({ strapi }) => ({
  async processScript(ctx) {
    try {
      const { script, promptId, sessionId } = ctx.request.body;

      console.log('sessionId:', sessionId);

      // Validate user token
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized('Invalid or expired authorization token');
      }

      if (!script) {
        return ctx.badRequest('Script content is required');
      }

      // Get or create active session if no specific sessionId is provided
      let session;
      if (!sessionId) {
        // Look for active session
        session = await strapi
          .service('api::aiscript-session.aiscript-session')
          .getActiveSession(user.id);

        if (!session) {
          // Create new session if none exists
          session = await strapi
            .service('api::aiscript-session.aiscript-session')
            .createSession({ userId: user.id });
        }
      } else {
        // Use the provided sessionId
        session = await strapi
          .service('api::aiscript-session.aiscript-session')
          .getActiveSessionById(sessionId);

        if (!session) {
          return ctx.notFound('Session not found');
        }
      }

      // Populate user information for the session if not already populated
      if (session && !session.users_permissions_user) {
        const populatedSession = await strapi.entityService.findOne(
          'api::aiscript-session.aiscript-session',
          session.id,
          {
            populate: ['users_permissions_user'],
          }
        );
        session = populatedSession;
      }

      console.log('session:', session);

      // Process the script with AI
      const response = await strapi.service('api::aiscript.aiscript').processUserScript({
        message: script,
        session,
        promptId,
        isUseDefaultPrompt: sessionId ? false : true,
      });

      return {
        message: response,
        session_id: session ? session.session_id : sessionId || null,
      };
    } catch (err) {
      console.error('Error processing script:', err);
      return ctx.badRequest('Failed to process script', { error: err });
    }
  },
}));
