// import type { Core } from '@strapi/strapi';

export default {
  /**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
  register(/* { strapi }: { strapi: Core.Strapi } */) {},

  /**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
  async bootstrap({ strapi }) {
    // log current environment
    console.log('Current environment:', strapi.config.environment);

    // Sync user subscription tiers from tracking records
    await syncUserSubscriptionTiers(strapi);

    // Log cron tasks when they are triggered
    const cronTasks = strapi.cron.jobs;
    // Iterate over each cron task
    const airtableSync = cronTasks.find((task) => task.name === 'airtableSync');
    // update new rule for airtableSync
    if (airtableSync) {
      const airtableService = strapi.service('api::airtable.airtable');
      // Call the syncAffiliates method from the service
      const rule = await airtableService.getAirtableCronSchedule();
      console.log({ rule });

      // Update the rule correctly
      airtableSync.options.rule = rule;

      // Restart the cron job to apply the new schedule
      if (airtableSync.job.stop && airtableSync.job.start) {
        airtableSync.job.stop();
        airtableSync.job.start();
      }

      console.log('Updated airtableSync rule:', airtableSync.options.rule);
    } else {
      console.error('airtableSync task not found');
    }
  },
};

/**
 * Syncs subscription tiers from user-tracking-request to users
 * This ensures all users have their subscription_tier field properly set
 */
async function syncUserSubscriptionTiers(strapi) {
  try {
    console.log('Starting subscription tier sync...');

    // Get all tracking records with their subscription tiers and users
    const trackingRecords = await strapi.entityService.findMany(
      'api::user-tracking-request.user-tracking-request',
      {
        populate: ['subscription_tier', 'users_permissions_user'],
      }
    );

    console.log(`Found ${trackingRecords.length} tracking records to process`);
    let updatedCount = 0;
    let skippedCount = 0;

    // Process each tracking record
    for (const record of trackingRecords) {
      // Skip if no user or subscription tier
      if (!record.users_permissions_user || !record.subscription_tier) {
        skippedCount++;
        continue;
      }

      const userId = record.users_permissions_user.id;
      const tierId = record.subscription_tier.id;

      // Update the user with the subscription tier
      try {
        await strapi.entityService.update('plugin::users-permissions.user', userId, {
          data: {
            subscription_tier: tierId,
          },
        });
        updatedCount++;
      } catch (error) {
        console.error(`Error updating user ${userId} subscription tier:`, error);
      }
    }

    console.log(
      `Subscription tier sync completed: ${updatedCount} users updated, ${skippedCount} records skipped`
    );
  } catch (error) {
    console.error('Error during subscription tier sync:', error);
  }
}
