import React from "react";
import { Loader2, CreditCard, Building2, AlertCircle, CheckCircle, XCircle } from "lucide-react";

interface PaymentInfo {
  paypal_email?: string;
  bank_transfer?: {
    // Vietnamese banking fields (new format)
    account_holder_name?: string;
    bank_name?: string;
    stk?: string;
    // International banking fields (legacy format for backward compatibility)
    first_name?: string;
    last_name?: string;
    business_name?: string;
    country?: string;
    city?: string;
    state?: string;
    address?: string;
    zip_code?: string;
    account_number?: string;
    swift_code?: string;
  };
}

interface PaymentInfoDisplayProps {
  paymentInfo: PaymentInfo | null;
  selectedMethod: "paypal" | "bank transfer" | "";
  loading: boolean;
  error: string | null;
  hasPaymentInfo: boolean;
  availableMethods: string[];
}

const PaymentInfoDisplay: React.FC<PaymentInfoDisplayProps> = ({
  paymentInfo,
  selectedMethod,
  loading,
  error,
  hasPaymentInfo,
  availableMethods,
}) => {
  // Debug logging
  console.log("PaymentInfoDisplay debug:", {
    selectedMethod,
    hasPaymentInfo,
    availableMethods,
    paymentInfo,
    loading,
    error
  });

  // Don't render anything if no method is selected
  if (!selectedMethod) {
    return null;
  }

  // Loading state
  if (loading) {
    return (
      <div className="mt-4 p-4 bg-gray-50 dark:bg-[#23262f] border border-gray-200 dark:border-[#353945] rounded-md">
        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span>Loading payment information...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-md">
        <div className="flex items-center gap-2 text-sm text-red-600 dark:text-red-400">
          <XCircle className="h-4 w-4" />
          <span>Error loading payment information: {error}</span>
        </div>
      </div>
    );
  }

  // Check if the selected method is available
  const isMethodAvailable = availableMethods.includes(selectedMethod);

  if (!isMethodAvailable) {
    return (
      <div className="mt-4 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-700 rounded-md">
        <div className="flex items-center gap-2 text-sm text-amber-600 dark:text-amber-400">
          <AlertCircle className="h-4 w-4" />
          <span>
            {selectedMethod === "paypal" 
              ? "PayPal email address not configured for this partner"
              : "Bank transfer details not configured for this partner"
            }
          </span>
        </div>
        <p className="text-xs text-amber-500 dark:text-amber-400 mt-1">
          Please ask the partner to complete their payment information setup.
        </p>
      </div>
    );
  }

  // Render payment information based on selected method
  return (
    <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-md">
      <div className="flex items-center gap-2 mb-3">
        <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
        <span className="text-sm font-medium text-green-800 dark:text-green-300">
          Payment Information
        </span>
      </div>

      {selectedMethod === "paypal" && paymentInfo?.paypal_email && (
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
            <CreditCard className="h-4 w-4 text-blue-600" />
            <span className="font-medium">PayPal</span>
          </div>
          <div className="ml-6">
            <div className="text-sm">
              <span className="text-gray-600 dark:text-gray-400">Email: </span>
              <span className="font-mono text-gray-900 dark:text-gray-100">
                {paymentInfo.paypal_email}
              </span>
            </div>
          </div>
        </div>
      )}

      {selectedMethod === "bank transfer" && paymentInfo?.bank_transfer && (
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
            <Building2 className="h-4 w-4 text-blue-600" />
            <span className="font-medium">Chuyển khoản ngân hàng</span>
          </div>
          <div className="ml-6 space-y-2">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
              {/* Vietnamese format (prioritized) */}
              {paymentInfo.bank_transfer.account_holder_name && (
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Chủ tài khoản: </span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {paymentInfo.bank_transfer.account_holder_name}
                  </span>
                </div>
              )}
              {/* Fallback to international format for account holder */}
              {!paymentInfo.bank_transfer.account_holder_name && paymentInfo.bank_transfer.first_name && paymentInfo.bank_transfer.last_name && (
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Account Holder: </span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {paymentInfo.bank_transfer.first_name} {paymentInfo.bank_transfer.last_name}
                  </span>
                </div>
              )}

              {paymentInfo.bank_transfer.bank_name && (
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Tên ngân hàng: </span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {paymentInfo.bank_transfer.bank_name}
                  </span>
                </div>
              )}

              {/* Vietnamese STK field (prioritized) */}
              {paymentInfo.bank_transfer.stk && (
                <div>
                  <span className="text-gray-600 dark:text-gray-400">STK: </span>
                  <span className="font-mono text-gray-900 dark:text-gray-100">
                    {paymentInfo.bank_transfer.stk}
                  </span>
                </div>
              )}
              {/* Fallback to international account number */}
              {!paymentInfo.bank_transfer.stk && paymentInfo.bank_transfer.account_number && (
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Account Number: </span>
                  <span className="font-mono text-gray-900 dark:text-gray-100">
                    {paymentInfo.bank_transfer.account_number}
                  </span>
                </div>
              )}

              {/* Legacy international fields (only show if Vietnamese fields are not present) */}
              {!paymentInfo.bank_transfer.account_holder_name && !paymentInfo.bank_transfer.bank_name && !paymentInfo.bank_transfer.stk && (
                <>
                  {paymentInfo.bank_transfer.business_name && (
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Business: </span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {paymentInfo.bank_transfer.business_name}
                      </span>
                    </div>
                  )}
                  {paymentInfo.bank_transfer.swift_code && (
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">SWIFT Code: </span>
                      <span className="font-mono text-gray-900 dark:text-gray-100">
                        {paymentInfo.bank_transfer.swift_code}
                      </span>
                    </div>
                  )}
                  {paymentInfo.bank_transfer.country && (
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Country: </span>
                      <span className="text-gray-900 dark:text-gray-100">
                        {paymentInfo.bank_transfer.country}
                      </span>
                    </div>
                  )}
                  {paymentInfo.bank_transfer.address && (
                    <div className="sm:col-span-2">
                      <span className="text-gray-600 dark:text-gray-400">Address: </span>
                      <span className="text-gray-900 dark:text-gray-100">
                        {paymentInfo.bank_transfer.address}
                        {paymentInfo.bank_transfer.city && `, ${paymentInfo.bank_transfer.city}`}
                        {paymentInfo.bank_transfer.state && `, ${paymentInfo.bank_transfer.state}`}
                        {paymentInfo.bank_transfer.zip_code && ` ${paymentInfo.bank_transfer.zip_code}`}
                      </span>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="mt-3 pt-2 border-t border-green-200 dark:border-green-700">
        <p className="text-xs text-green-600 dark:text-green-400">
          This payment information will be included in the payout record for processing.
        </p>
      </div>
    </div>
  );
};

export default PaymentInfoDisplay;
