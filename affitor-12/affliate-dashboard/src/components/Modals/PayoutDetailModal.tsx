"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  X,
  User,
  CreditCard,
  Building2,
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
  Copy,
} from "lucide-react";
import { toast } from "react-hot-toast";

interface PayoutDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  payout: PayoutDetailData | null;
}

interface PayoutDetailData {
  id: number;
  documentId: string;
  partner: {
    id: number;
    user: {
      id: number;
      username: string;
      email: string;
      first_name?: string;
      last_name?: string;
    };
  };
  payment_method: string;
  commission_cycle: string;
  amount: number;
  payout_status: "pending" | "approved" | "completed";
  createdAt: string;
  updatedAt: string;
  payout_date?: string | null;
  payment_info?: {
    paypal_email?: string;
    bank_transfer?: {
      // Vietnamese banking fields (new format)
      account_holder_name?: string;
      bank_name?: string;
      stk?: string;
      // International banking fields (legacy format for backward compatibility)
      first_name?: string;
      last_name?: string;
      business_name?: string;
      country?: string;
      city?: string;
      state?: string;
      address?: string;
      zip_code?: string;
      account_number?: string;
      swift_code?: string;
    };
  };
}

export default function PayoutDetailModal({
  isOpen,
  onClose,
  payout,
}: PayoutDetailModalProps) {
  if (!payout) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getPartnerName = (partner: PayoutDetailData["partner"]) => {
    const { first_name, last_name, username } = partner.user;
    if (first_name && last_name) {
      return `${first_name} ${last_name}`;
    }
    return username;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "approved":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "completed":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4" />;
      case "approved":
        return <CheckCircle className="h-4 w-4" />;
      case "completed":
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  const renderPaymentInfo = () => {
    if (!payout.payment_info) {
      return (
        <div className="text-center py-4 text-gray-500 dark:text-gray-400">
          No payment information available
        </div>
      );
    }

    const { paypal_email, bank_transfer } = payout.payment_info;

    if (payout.payment_method === "paypal" && paypal_email) {
      return (
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            <CreditCard className="h-4 w-4" />
            PayPal Information
          </div>
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-xs text-gray-500 dark:text-gray-400">Email Address</label>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {paypal_email}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(paypal_email, "PayPal email")}
                className="h-8 w-8 p-0"
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      );
    }

    if (payout.payment_method === "bank transfer" && bank_transfer) {
      // Check if it's Vietnamese format (new) or international format (legacy)
      const isVietnameseFormat = bank_transfer.account_holder_name && bank_transfer.bank_name && bank_transfer.stk;

      return (
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
            <Building2 className="h-4 w-4" />
            Bank Transfer Information
          </div>
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-4">
            {isVietnameseFormat ? (
              // Vietnamese banking format
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-xs text-gray-500 dark:text-gray-400">Chủ tài khoản</label>
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {bank_transfer.account_holder_name}
                      </p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(bank_transfer.account_holder_name!, "Account holder name")}
                        className="h-6 w-6 p-0"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <div>
                    <label className="text-xs text-gray-500 dark:text-gray-400">Tên ngân hàng</label>
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {bank_transfer.bank_name}
                      </p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(bank_transfer.bank_name!, "Bank name")}
                        className="h-6 w-6 p-0"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
                <div>
                  <label className="text-xs text-gray-500 dark:text-gray-400">STK (Số tài khoản)</label>
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 font-mono">
                      {bank_transfer.stk}
                    </p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(bank_transfer.stk!, "Account number")}
                      className="h-6 w-6 p-0"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              // International banking format (legacy)
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {bank_transfer.first_name && (
                    <div>
                      <label className="text-xs text-gray-500 dark:text-gray-400">First Name</label>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {bank_transfer.first_name}
                      </p>
                    </div>
                  )}
                  {bank_transfer.last_name && (
                    <div>
                      <label className="text-xs text-gray-500 dark:text-gray-400">Last Name</label>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {bank_transfer.last_name}
                      </p>
                    </div>
                  )}
                  {bank_transfer.business_name && (
                    <div className="md:col-span-2">
                      <label className="text-xs text-gray-500 dark:text-gray-400">Business Name</label>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {bank_transfer.business_name}
                      </p>
                    </div>
                  )}
                  {bank_transfer.account_number && (
                    <div>
                      <label className="text-xs text-gray-500 dark:text-gray-400">Account Number</label>
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100 font-mono">
                          {bank_transfer.account_number}
                        </p>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(bank_transfer.account_number!, "Account number")}
                          className="h-6 w-6 p-0"
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  )}
                  {bank_transfer.swift_code && (
                    <div>
                      <label className="text-xs text-gray-500 dark:text-gray-400">SWIFT Code</label>
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100 font-mono">
                          {bank_transfer.swift_code}
                        </p>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(bank_transfer.swift_code!, "SWIFT code")}
                          className="h-6 w-6 p-0"
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
                {(bank_transfer.address || bank_transfer.city || bank_transfer.state || bank_transfer.country) && (
                  <div>
                    <label className="text-xs text-gray-500 dark:text-gray-400">Address</label>
                    <p className="text-sm text-gray-900 dark:text-gray-100">
                      {[bank_transfer.address, bank_transfer.city, bank_transfer.state, bank_transfer.zip_code, bank_transfer.country]
                        .filter(Boolean)
                        .join(", ")}
                    </p>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      );
    }

    return (
      <div className="text-center py-4 text-gray-500 dark:text-gray-400">
        Payment information not available for this method
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Payout Details</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Payout Overview */}
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-xs text-gray-500 dark:text-gray-400">Payout ID</label>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 font-mono">
                  {payout.documentId}
                </p>
              </div>
              <div>
                <label className="text-xs text-gray-500 dark:text-gray-400">Amount</label>
                <p className="text-lg font-bold text-gray-900 dark:text-gray-100">
                  {formatCurrency(payout.amount)}
                </p>
              </div>
              <div>
                <label className="text-xs text-gray-500 dark:text-gray-400">Status</label>
                <div className="flex items-center gap-2 mt-1">
                  <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(payout.payout_status)}`}>
                    {getStatusIcon(payout.payout_status)}
                    {payout.payout_status.charAt(0).toUpperCase() + payout.payout_status.slice(1)}
                  </span>
                </div>
              </div>
              <div>
                <label className="text-xs text-gray-500 dark:text-gray-400">Payment Method</label>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {payout.payment_method}
                </p>
              </div>
            </div>
          </div>

          {/* Partner Information */}
          <div>
            <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              <User className="h-4 w-4" />
              Partner Information
            </div>
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-xs text-gray-500 dark:text-gray-400">Name</label>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {getPartnerName(payout.partner)}
                  </p>
                </div>
                <div>
                  <label className="text-xs text-gray-500 dark:text-gray-400">Email</label>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {payout.partner.user.email}
                  </p>
                </div>
                <div>
                  <label className="text-xs text-gray-500 dark:text-gray-400">Username</label>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {payout.partner.user.username}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Information */}
          <div>
            {renderPaymentInfo()}
          </div>

          {/* Timeline */}
          <div>
            <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              <Calendar className="h-4 w-4" />
              Timeline
            </div>
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-3">
              <div>
                <label className="text-xs text-gray-500 dark:text-gray-400">Created</label>
                <p className="text-sm text-gray-900 dark:text-gray-100">
                  {formatDate(payout.createdAt)}
                </p>
              </div>
              <div>
                <label className="text-xs text-gray-500 dark:text-gray-400">Last Updated</label>
                <p className="text-sm text-gray-900 dark:text-gray-100">
                  {formatDate(payout.updatedAt)}
                </p>
              </div>
              {payout.payout_date && (
                <div>
                  <label className="text-xs text-gray-500 dark:text-gray-400">Payout Date</label>
                  <p className="text-sm text-gray-900 dark:text-gray-100">
                    {formatDate(payout.payout_date)}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
