"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { ArrowLeft, Edit, Trash2 } from 'lucide-react';
import { UserPrompt, actions } from '@/features/aiscript/aiscript.slice';

interface PromptDetailViewProps {
  prompt: UserPrompt;
  onClose: () => void;
  onBack: () => void;
  onUse: (prompt: UserPrompt) => void;
  onPopulateInput?: (content: string) => void;
}

export default function PromptDetailView({ prompt, onClose, onBack, onUse, onPopulateInput }: PromptDetailViewProps) {
  const dispatch = useDispatch();
  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState(prompt.title);
  const [editedContent, setEditedContent] = useState(prompt.content);
  const [isSaving, setIsSaving] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const titleInputRef = useRef<HTMLInputElement>(null);

  // Auto-focus title input when entering edit mode
  useEffect(() => {
    if (isEditing && titleInputRef.current) {
      titleInputRef.current.focus();
      titleInputRef.current.select();
    }
  }, [isEditing]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    if (!editedTitle.trim() || !editedContent.trim()) {
      return;
    }

    setIsSaving(true);
    try {
      await dispatch(actions.updateUserPromptRequest({
        id: prompt.id,
        data: {
          title: editedTitle.trim(),
          content: editedContent.trim(),
          description: prompt.description || '',
          tags: prompt.tags || [],
          isFavorite: prompt.isFavorite,
        },
      }));
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving prompt:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditedTitle(prompt.title);
    setEditedContent(prompt.content);
    setIsEditing(false);
  };

  const handleUsePrompt = () => {
    if (onPopulateInput) {
      onPopulateInput(prompt.content);
    } else {
      onUse(prompt);
    }
    onClose(); // Close the prompt manager
  };

  const handleDelete = () => {
    setShowDeleteConfirm(true);
  };

  const handleConfirmDelete = async () => {
    setIsDeleting(true);
    try {
      await dispatch(actions.deleteUserPromptRequest(prompt.id));
      onBack(); // Go back to list after deletion
    } catch (error) {
      console.error('Error deleting prompt:', error);
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
  };

  return (
    <div className="h-full flex flex-col overflow-hidden bg-white dark:bg-gray-900 relative">
      {/* Header - Always visible */}
      <div className="flex items-center p-2 sm:p-3 md:p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 flex-shrink-0 gap-2">
        <button
          onClick={onBack}
          className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-all duration-200 hover:scale-105 active:scale-95 flex-shrink-0"
        >
          <ArrowLeft size={18} className="text-gray-500 dark:text-gray-400" />
        </button>

        {/* Title Section - Flexible but with minimum space */}
        <div className="flex-1 min-w-0 mr-2">
          {isEditing ? (
            <input
              ref={titleInputRef}
              type="text"
              value={editedTitle}
              onChange={(e) => setEditedTitle(e.target.value)}
              className="w-full text-sm sm:text-base md:text-lg font-semibold text-gray-900 dark:text-gray-100 bg-transparent border-none outline-none focus:ring-0 p-0"
              placeholder="Enter prompt title..."
            />
          ) : (
            <h2 className="text-sm sm:text-base md:text-lg font-semibold text-gray-900 dark:text-gray-100 line-clamp-1">
              {prompt.title}
            </h2>
          )}
        </div>

        {/* Action Buttons - Always visible with responsive sizing */}
        <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0">
          {isEditing ? (
            <>
              <button
                onClick={handleCancel}
                disabled={isSaving}
                className="px-2 sm:px-3 py-1 sm:py-1.5 text-xs sm:text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors disabled:opacity-50 whitespace-nowrap"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isSaving || !editedTitle.trim() || !editedContent.trim()}
                className="px-2 sm:px-3 py-1 sm:py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs sm:text-sm rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
              >
                {isSaving ? 'Saving...' : 'Save'}
              </button>
            </>
          ) : (
            <>
              <button
                onClick={handleEdit}
                className="p-1 sm:p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-all duration-200 hover:scale-105 active:scale-95"
                title="Edit prompt"
              >
                <Edit size={16} className="sm:w-[18px] sm:h-[18px] text-gray-500 dark:text-gray-400" />
              </button>
              <button
                onClick={handleDelete}
                className="p-1 sm:p-1.5 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-md transition-all duration-200 hover:scale-105 active:scale-95"
                title="Delete prompt"
              >
                <Trash2 size={16} className="sm:w-[18px] sm:h-[18px] text-red-500 dark:text-red-400" />
              </button>
            </>
          )}
        </div>
      </div>

      {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto p-3 sm:p-6 min-h-0">
        <div className="pb-20">
          {isEditing ? (
            <textarea
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              className="w-full h-full min-h-[200px] sm:min-h-[300px] p-3 sm:p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none text-sm sm:text-base"
              placeholder="Enter your prompt content here..."
            />
          ) : (
            <div className="prose prose-gray dark:prose-invert max-w-none prose-sm sm:prose-base">
              <div className="whitespace-pre-wrap text-gray-900 dark:text-gray-100 leading-relaxed">
                {prompt.content}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer - Sticky at bottom */}
      {!isEditing && (
        <div className="sticky bottom-0 p-3 sm:p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 shadow-lg">
          <div className="flex justify-end">
            <button
              onClick={handleUsePrompt}
              className="px-4 sm:px-6 py-2 sm:py-2.5 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors shadow-sm hover:shadow-md text-sm sm:text-base"
            >
              Use This Prompt
            </button>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Delete Prompt
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Are you sure you want to delete "{prompt.title}"? This action cannot be undone.
              </p>
              <div className="flex gap-3 justify-end">
                <button
                  onClick={handleCancelDelete}
                  disabled={isDeleting}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleConfirmDelete}
                  disabled={isDeleting}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
