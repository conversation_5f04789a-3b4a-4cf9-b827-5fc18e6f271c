"use client";

import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Save, X, Star, AlertCircle } from 'lucide-react';
import { UserPrompt, actions } from '@/features/aiscript/aiscript.slice';

interface PromptEditorProps {
  prompt?: UserPrompt | null;
  onSave: () => void;
  onCancel: () => void;
}

export default function PromptEditor({ prompt, onSave, onCancel }: PromptEditorProps) {
  const dispatch = useDispatch();
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    isFavorite: false,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [warnings, setWarnings] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Initialize form data when prompt changes
  useEffect(() => {
    if (prompt) {
      setFormData({
        title: prompt.title,
        content: prompt.content,
        isFavorite: prompt.isFavorite,
      });
    } else {
      setFormData({
        title: '',
        content: '',
        isFavorite: false,
      });
    }
    setErrors({});
    setWarnings({});
    setTouched({});
  }, [prompt]);

  // Validate individual field (for real-time validation)
  const validateField = (fieldName: string, value: string) => {
    const newErrors = { ...errors };
    const newWarnings = { ...warnings };

    // Clear previous errors and warnings for this field
    delete newErrors[fieldName];
    delete newWarnings[fieldName];

    if (fieldName === 'title') {
      // Title validation - mirrors backend exactly
      if (!value.trim()) {
        newErrors.title = 'Title is required';
      } else if (value.length < 3) {
        newErrors.title = 'Title must be between 3 and 100 characters';
      } else if (value.length > 100) {
        newErrors.title = 'Title must be between 3 and 100 characters';
      } else if (value.length > 80) {
        newWarnings.title = 'Consider a shorter title for better readability';
      }
    }

    if (fieldName === 'content') {
      // Content validation - mirrors backend exactly
      if (!value.trim()) {
        newErrors.content = 'Content is required';
      } else if (value.length < 10) {
        newErrors.content = 'Content must be between 10 and 5000 characters';
      } else if (value.length > 5000) {
        newErrors.content = 'Content must be between 10 and 5000 characters';
      } else if (value.length > 4500) {
        newWarnings.content = `${5000 - value.length} characters remaining`;
      }
    }

    setErrors(newErrors);
    setWarnings(newWarnings);
    return Object.keys(newErrors).length === 0;
  };

  // Validate entire form (for submission)
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Title validation - exact backend mirror
    if (!formData.title.trim()) {
      newErrors.title = 'Title and content are required'; // Backend message
    } else if (formData.title.length < 3 || formData.title.length > 100) {
      newErrors.title = 'Title must be between 3 and 100 characters'; // Backend message
    }

    // Content validation - exact backend mirror
    if (!formData.content.trim()) {
      newErrors.content = 'Title and content are required'; // Backend message
    } else if (formData.content.length < 10 || formData.content.length > 5000) {
      newErrors.content = 'Content must be between 10 and 5000 characters'; // Backend message
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input changes with real-time validation
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, title: value }));
    setTouched(prev => ({ ...prev, title: true }));

    // Real-time validation
    if (touched.title || value.length > 0) {
      validateField('title', value);
    }
  };

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setFormData(prev => ({ ...prev, content: value }));
    setTouched(prev => ({ ...prev, content: true }));

    // Real-time validation
    if (touched.content || value.length > 0) {
      validateField('content', value);
    }
  };

  // Handle field blur for validation
  const handleFieldBlur = (fieldName: string) => {
    setTouched(prev => ({ ...prev, [fieldName]: true }));
    const value = fieldName === 'title' ? formData.title : formData.content;
    validateField(fieldName, value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Mark all fields as touched to show validation errors
    setTouched({ title: true, content: true });

    // Validate all fields
    const isTitleValid = validateField('title', formData.title);
    const isContentValid = validateField('content', formData.content);
    const isFormValid = validateForm();

    if (!isFormValid || !isTitleValid || !isContentValid) {
      // Focus on first field with error
      if (errors.title) {
        document.getElementById('title')?.focus();
      } else if (errors.content) {
        document.getElementById('content')?.focus();
      }
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data with required fields - exact backend format
      const promptData = {
        title: formData.title.trim(),
        content: formData.content.trim(),
        description: '', // Set empty description as default
        tags: [], // Set empty tags array as default
        isFavorite: formData.isFavorite,
      };

      if (prompt) {
        // Update existing prompt
        dispatch(actions.updateUserPromptRequest({
          id: prompt.id,
          data: promptData,
        }));
      } else {
        // Create new prompt
        dispatch(actions.createUserPrompt(promptData));
      }
      onSave();
    } catch (error) {
      console.error('Error saving prompt:', error);
      // Set a general error if API call fails
      setErrors(prev => ({
        ...prev,
        submit: 'Failed to save prompt. Please try again.'
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="h-full flex flex-col overflow-hidden">
      {/* Header - Always visible */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {prompt ? 'Edit Prompt' : 'Create New Prompt'}
        </h3>
        <button
          onClick={onCancel}
          className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md transition-colors"
        >
          <X size={20} />
        </button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="flex-1 flex flex-col overflow-hidden min-h-0">
        <div className="flex-1 flex flex-col p-4 gap-4 overflow-y-auto min-h-0">
          <div className="pb-20">
            {/* Title */}
            <div className="flex-shrink-0 mb-4">
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Title *
              </label>
              <input
                type="text"
                id="title"
                value={formData.title}
                onChange={handleTitleChange}
                onBlur={() => handleFieldBlur('title')}
                className={`w-full px-3 py-2.5 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors ${
                  errors.title
                    ? 'border-red-500 focus:ring-red-500'
                    : warnings.title
                    ? 'border-yellow-400 focus:ring-yellow-500'
                    : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
                }`}
                placeholder="Enter a descriptive title for your prompt"
                maxLength={100}
              />
              {errors.title && (
                <p className="mt-1.5 text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle size={14} />
                  {errors.title}
                </p>
              )}
              {!errors.title && warnings.title && (
                <p className="mt-1.5 text-sm text-yellow-600 dark:text-yellow-400 flex items-center gap-1">
                  <AlertCircle size={14} />
                  {warnings.title}
                </p>
              )}
              <div className="mt-1 flex justify-between items-center">
                <div />
                <span className={`text-xs transition-colors ${
                  formData.title.length > 90
                    ? 'text-red-500'
                    : formData.title.length > 80
                    ? 'text-yellow-600 dark:text-yellow-400'
                    : 'text-gray-500 dark:text-gray-400'
                }`}>
                  {formData.title.length}/100
                </span>
              </div>
            </div>



            {/* Content */}
            <div className="flex-1 flex flex-col min-h-0">
              <label htmlFor="content" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex-shrink-0">
                Prompt Content *
              </label>
              <textarea
                id="content"
                value={formData.content}
                onChange={handleContentChange}
                onBlur={() => handleFieldBlur('content')}
                className={`flex-1 w-full px-3 py-2.5 border rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 resize-none transition-colors min-h-[120px] ${
                  errors.content
                    ? 'border-red-500 focus:ring-red-500'
                    : warnings.content
                    ? 'border-yellow-400 focus:ring-yellow-500'
                    : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500'
                }`}
                placeholder="Enter your prompt content here..."
                maxLength={5000}
              />
              <div className="mt-2 space-y-1 flex-shrink-0">
                {errors.content && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle size={14} />
                    {errors.content}
                  </p>
                )}
                {!errors.content && warnings.content && (
                  <p className="text-sm text-yellow-600 dark:text-yellow-400 flex items-center gap-1">
                    <AlertCircle size={14} />
                    {warnings.content}
                  </p>
                )}
                <div className="flex justify-between items-center">
                  <div />
                  <span className={`text-xs transition-colors ${
                    formData.content.length > 4800
                      ? 'text-red-500'
                      : formData.content.length > 4500
                      ? 'text-yellow-600 dark:text-yellow-400'
                      : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {formData.content.length}/5000
                  </span>
                </div>
              </div>
            </div>
          </div>



          {/* Favorite */}
          {/* <div className="flex-shrink-0">
            <label className="flex items-center gap-2 cursor-pointer py-1">
              <input
                type="checkbox"
                checked={formData.isFavorite}
                onChange={(e) => setFormData(prev => ({ ...prev, isFavorite: e.target.checked }))}
                className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
              />
              <Star size={16} className={formData.isFavorite ? 'text-yellow-500' : 'text-gray-400'} />
              <span className="text-sm text-gray-700 dark:text-gray-300">Mark as favorite</span>
            </label>
          </div> */}
        </div>

        {/* Footer - Sticky at bottom */}
        <div className="sticky bottom-0 px-4 py-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 shadow-lg">
          {/* Submit Error */}
          {errors.submit && (
            <div className="mb-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-sm text-red-600 dark:text-red-400 flex items-center gap-2">
                <AlertCircle size={16} />
                {errors.submit}
              </p>
            </div>
          )}

          <div className="flex items-center justify-end gap-3">
            <button
              type="button"
              onClick={onCancel}
              disabled={isSubmitting}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || Object.keys(errors).filter(key => key !== 'submit').length > 0}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
            >
              <Save size={16} />
              {isSubmitting ? 'Saving...' : prompt ? 'Update Prompt' : 'Create Prompt'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
