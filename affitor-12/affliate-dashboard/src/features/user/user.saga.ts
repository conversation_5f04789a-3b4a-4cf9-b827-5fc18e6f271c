import { call, put, takeEvery, takeLatest, select } from "redux-saga/effects";
import { actions, UpdateProfilePayload } from "./user.slice";
import { actions as subscriptionActions } from "../subscription-tier/subscription-tier.slice";
import { actions as authActions } from "../auth/auth.slice";
import axios from "axios";
import Router from "next/router";

// Function to fetch user data from API proxy
const fetchUserData = async (token: string) => {
  console.log(`🍪 Making request with all cookies automatically included`);

  const response = await axios.get("/api/users/me", {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    withCredentials: true, // This ensures ALL cookies are sent automatically
  });
  return response.data;
};

// New function to update user profile
const updateUserProfileData = async (
  profileData: UpdateProfilePayload,
  token: string
) => {
  const response = await axios.put("/api/users/profile", profileData, {
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });
  return response.data;
};

// Function to fetch admin users data
const fetchAdminUsersData = async (
  page: number,
  pageSize: number,
  search: string,
  status: string,
  token: string
) => {
  const response = await axios.get("/api/admin/users", {
    params: {
      page,
      pageSize,
      search,
      status,
    },
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return response.data;
};

function* handleFetchUserMe(): Generator<any, void, any> {
  try {
    // Get token directly from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    if (!token) {
      yield put(actions.setUserError("Authentication required"));
      return;
    }

    // Call the API proxy instead of directly calling StrapiClient
    const response = yield call(fetchUserData, token);

    // Calculate isPremium based on subscription tier price
    const subscriptionTier =
      response?.subscription_tier ||
      response?.user_tracking_request?.subscription_tier;
    const isPremium = !!subscriptionTier && subscriptionTier.price > 0;
    const isReferrer = !!response?.referrer;

    // Augment the response with isPremium flag
    const userData = {
      ...response,
      isPremium,
      isReferrer,
    };

    // Set user data with isPremium flag
    yield put(actions.setUserData(userData));

    // Extract subscription data if available
    if (response?.user_tracking_request?.subscription_tier) {
      const userTracking = response.user_tracking_request;
      const subscriptionTier = userTracking.subscription_tier;

      // Prepare current subscription data with relevant information
      const currentSubscription = {
        ...subscriptionTier,
        request_count: userTracking.request_count,
      };

      // Set current subscription in the store
      yield put(
        subscriptionActions.setCurrentSubscription(currentSubscription)
      );
    } else {
      // Clear current subscription if not found
      yield put(subscriptionActions.setCurrentSubscription(null));
    }
  } catch (error: any) {
    console.error("Error fetching user data:", error);

    // Handle 401 Unauthorized errors - redirect to login
    if (error.response?.status === 401) {
      console.log("401 error detected, logging out and redirecting to authentication");

      // Clear auth data
      yield put(authActions.logout());
      yield put(actions.clearUserData());

      // Redirect to authentication page
      Router.push("/authentication");

      // Set specific error message for 401
      yield put(actions.setUserError("Session expired. Please log in again."));
      return;
    }

    const errorMessage = error.message || "Failed to fetch user data";
    yield put(actions.setUserError(errorMessage));
  }
}

// New saga handler for updating user profile
function* handleUpdateUserProfile(action: {
  type: string;
  payload: UpdateProfilePayload;
}): Generator<any, void, any> {
  try {
    // Get token directly from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    if (!token) {
      yield put(actions.updateUserProfileFailure("Authentication required"));
      return;
    }



    // Filter out empty, null, or undefined values to only update fields with actual data
    // Special handling for payment methods where null values are intentional (to clear other methods)
    const filteredPayload = Object.fromEntries(
      Object.entries(action.payload).filter(([key, value]) => {
        // Special handling for payment method fields - allow null values to clear other methods
        if (key === 'paypal_email' || key === 'bank_transfer') {
          return true; // Always include payment method fields, even if null
        }

        // For other fields, only include fields that have meaningful values
        return (
          value !== null &&
          value !== undefined &&
          value !== "" &&
          (typeof value === "string" ? value.trim() !== "" : true)
        );
      })
    );

    // Only proceed if there are fields to update
    if (Object.keys(filteredPayload).length === 0) {
      yield put(actions.updateUserProfileFailure("No fields to update"));
      return;
    }

    console.log("Original payload:", action.payload);
    console.log("Updating profile with filtered data:", filteredPayload);

    // Call the API proxy to update the profile with only filled fields
    yield call(
      updateUserProfileData,
      filteredPayload,
      token
    );

    // Mark update as successful
    yield put(actions.updateUserProfileSuccess());

    // Update the user data directly instead of triggering a new fetch
    // Get current user data from store
    const updatedCurrentUserData = yield select((state) => state.user.data);

    if (updatedCurrentUserData) {
      // Merge only the updated fields with existing user data
      const mergedUserData = {
        ...updatedCurrentUserData,
        ...filteredPayload,
      };

      // Update the user data in the store
      yield put(actions.setUserData(mergedUserData));
    }
  } catch (error: any) {
    console.error("Error updating user profile:", error);

    // Handle 401 Unauthorized errors - redirect to login
    if (error.response?.status === 401) {
      console.log("401 error detected during profile update, logging out and redirecting to authentication");

      // Clear auth data
      yield put(authActions.logout());
      yield put(actions.clearUserData());

      // Redirect to authentication page
      Router.push("/authentication");

      // Set specific error message for 401
      yield put(actions.updateUserProfileFailure("Session expired. Please log in again."));
      return;
    }

    const errorMessage = error.message || "Failed to update user profile";
    yield put(actions.updateUserProfileFailure(errorMessage));
  }
}

// New saga handler for fetching admin users
function* handleFetchAdminUsers(action: {
  type: string;
  payload: { page?: number; pageSize?: number; search?: string; status?: string };
}): Generator<any, void, any> {
  try {
    const { page = 1, pageSize = 10, search = "", status = "" } = action.payload;

    // Get admin token from localStorage
    const token =
      typeof window !== "undefined" ? localStorage.getItem("admin_token") : null;

    if (!token) {
      yield put(actions.fetchAdminUsersFailure("Admin authentication required"));
      return;
    }

    // Call the API to fetch users
    const response = yield call(
      fetchAdminUsersData,
      page,
      pageSize,
      search,
      status,
      token
    );

    yield put(actions.fetchAdminUsersSuccess(response));
  } catch (error: any) {
    console.error("Error fetching admin users:", error);
    const errorMessage = error.message || "Failed to fetch admin users";
    yield put(actions.fetchAdminUsersFailure(errorMessage));
  }
}

export default function* userSaga() {
  yield takeEvery(actions.fetchUserMe, handleFetchUserMe);
  yield takeLatest(actions.updateUserProfile, handleUpdateUserProfile);
  yield takeEvery(actions.fetchAdminUsers, handleFetchAdminUsers);
}
