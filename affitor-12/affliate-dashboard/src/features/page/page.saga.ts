import { call, put, takeLatest } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import axios from "axios";
import Router from "next/router";
import { actions } from "./page.slice";
import { ICreatePageData, IUpdatePageData } from "./page.slice";

const API_TIMEOUT = 30000; // 30 seconds

// Helper function to handle axios errors
function* handleAxiosError(error: any): Generator<any, string, any> {
  if (axios.isAxiosError(error)) {
    if (error.response) {
      const message = error.response.data?.message || error.response.data?.error?.message || error.message;
      return message;
    } else if (error.request) {
      return "Network error - please check your connection";
    }
  }
  return error.message || "An unexpected error occurred";
}

// Fetch pages saga
function* handleFetchPages(
  action: PayloadAction<{ page?: number; pageSize?: number; filters?: any }>
): Generator<any, void, any> {
  try {
    const { page = 1, pageSize = 10, filters = {} } = action.payload;
    
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.fetchPagesFailure('Authentication required'));
      return;
    }
    
    // Call the Next.js API route
    const response = yield call(axios.get, '/api/pages', {
      params: { page, pageSize, ...filters },
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT
    });
    
    // Handle response format - should be { data: pages[], meta: { pagination } }
    if (response?.data) {
      const responseData = response.data;

      // Check if we have the expected Strapi format
      if (responseData.data && Array.isArray(responseData.data)) {
        // Standard Strapi format: { data: pages, meta: { pagination } }
        yield put(actions.fetchPagesSuccess({
          pages: responseData.data,
          pagination: responseData.meta?.pagination || null
        }));
      } else if (Array.isArray(responseData)) {
        // Direct array format (fallback)
        yield put(actions.fetchPagesSuccess({
          pages: responseData,
          pagination: null
        }));
      } else {
        console.error('Unexpected response format:', responseData);
        yield put(actions.fetchPagesFailure('Unexpected response format'));
      }
    } else {
      yield put(actions.fetchPagesFailure('No data received'));
    }
  } catch (error: any) {
    const errorMessage = yield call(handleAxiosError, error);
    yield put(actions.fetchPagesFailure(errorMessage));
  }
}

// Fetch single page saga
function* handleFetchPage(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    const pageId = action.payload;

    console.log('🔄 [Page Saga] Starting single page fetch for ID:', pageId);
    console.log('🔄 [Page Saga] Action payload:', action.payload);

    // Get auth token from localStorage (optional for public access)
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    console.log('🔄 [Page Saga] Auth token check:', {
      hasToken: !!token,
      tokenPrefix: token ? token.substring(0, 10) + '...' : 'No token',
      isClient: typeof window !== 'undefined',
      isPublicAccess: !token
    });

    // Prepare headers - include auth if available
    const headers: any = {
      'Content-Type': 'application/json'
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Call the Next.js API route (now supports public access)
    console.log('🔄 [Page Saga] Making API call to:', `/api/pages/${pageId}`, 'with auth:', !!token);
    const response = yield call(axios.get, `/api/pages/${pageId}`, {
      headers,
      timeout: API_TIMEOUT
    });

    console.log('🔄 [Page Saga] API response received');
    console.log('🔄 [Page Saga] Response structure analysis:', {
      hasResponse: !!response,
      hasData: !!response?.data,
      dataType: typeof response?.data,
      isDataArray: Array.isArray(response?.data),
      hasDataProperty: !!response?.data?.data,
      dataPropertyType: typeof response?.data?.data,
      responseKeys: response?.data ? Object.keys(response.data) : [],
    });

    // Validate and sanitize content before storing
    const validatePageContent = (pageData: any) => {
      const timestamp = new Date().toISOString();
      console.log(`🔍 [Page Saga] [${timestamp}] VALIDATE CONTENT - Starting validation:`, {
        hasPageData: !!pageData,
        pageDataType: typeof pageData,
        pageDataKeys: pageData ? Object.keys(pageData) : 'N/A'
      });

      if (!pageData) {
        console.log(`🔍 [Page Saga] [${timestamp}] VALIDATE: No page data, returning as-is`);
        return pageData;
      }

      // If content exists, validate its structure
      if (pageData.content) {
        console.log(`🔍 [Page Saga] [${timestamp}] VALIDATE: Content exists, validating structure:`, {
          contentType: typeof pageData.content,
          isObject: typeof pageData.content === 'object',
          hasKeys: pageData.content && typeof pageData.content === 'object' ? Object.keys(pageData.content).length > 0 : false,
          contentKeys: pageData.content && typeof pageData.content === 'object' ? Object.keys(pageData.content) : [],
          contentValue: pageData.content
        });

        // More lenient content validation - only set to null if truly invalid
        if (!pageData.content) {
          console.log(`🔍 [Page Saga] [${timestamp}] VALIDATE: Content is null/undefined, keeping as null`);
          pageData.content = null;
        } else if (typeof pageData.content !== 'object') {
          console.log(`🔍 [Page Saga] [${timestamp}] VALIDATE: Content is not an object, setting to null`);
          pageData.content = null;
        } else if (Object.keys(pageData.content).length === 0) {
          console.log(`🔍 [Page Saga] [${timestamp}] VALIDATE: Content is empty object, setting to null`);
          pageData.content = null;
        } else {
          // More lenient Yoopta content structure validation
          console.log(`🔍 [Page Saga] [${timestamp}] VALIDATE: Checking Yoopta content structure...`);

          try {
            const isValidYooptaContent = Object.values(pageData.content).every((block: any, index: number) => {
              console.log(`🔍 [Page Saga] [${timestamp}] VALIDATE BLOCK ${index}:`, {
                block,
                hasBlock: !!block,
                blockType: typeof block,
                hasId: block && 'id' in block,
                hasType: block && 'type' in block,
                hasValue: block && 'value' in block,
                valueIsArray: block && Array.isArray(block.value)
              });

              // Basic block structure validation
              if (!block || typeof block !== 'object') return false;
              if (!('id' in block) || !('type' in block) || !('value' in block)) return false;
              if (!Array.isArray(block.value)) return false;

              // More lenient element validation
              return block.value.every((element: any, elemIndex: number) => {
                console.log(`🔍 [Page Saga] [${timestamp}] VALIDATE ELEMENT ${index}.${elemIndex}:`, {
                  element,
                  hasElement: !!element,
                  elementType: typeof element,
                  hasChildren: element && 'children' in element,
                  childrenIsArray: element && 'children' in element && Array.isArray(element.children),
                  hasOtherProps: element && (element.type || element.id || element.src || element.url)
                });

                if (!element || typeof element !== 'object') return false;

                // For elements with children, validate children structure
                if ('children' in element) {
                  if (!Array.isArray(element.children)) return false;
                  return element.children.every((child: any) =>
                    child && typeof child === 'object' && 'text' in child
                  );
                }

                // For elements without children (images, etc.), just check they have some valid properties
                return true; // Be more permissive for non-text elements
              });
            });

            if (!isValidYooptaContent) {
              console.warn(`🔍 [Page Saga] [${timestamp}] VALIDATE: Invalid Yoopta content structure detected, but preserving content for debugging`);
              // Don't set to null - let the editor handle it
              console.warn(`🔍 [Page Saga] [${timestamp}] VALIDATE: Content that failed validation:`, JSON.stringify(pageData.content, null, 2));
            } else {
              console.log(`🔍 [Page Saga] [${timestamp}] VALIDATE: Content validation passed`);
            }
          } catch (error) {
            console.error(`🔍 [Page Saga] [${timestamp}] VALIDATE: Error during validation:`, error);
            console.warn(`🔍 [Page Saga] [${timestamp}] VALIDATE: Preserving content despite validation error`);
          }
        }
      } else {
        console.log(`🔍 [Page Saga] [${timestamp}] VALIDATE: No content property found`);
      }

      console.log(`🔍 [Page Saga] [${timestamp}] VALIDATE: Final page data:`, {
        ...pageData,
        contentSummary: pageData.content ? 'Valid content' : 'No content'
      });

      return pageData;
    };

    // Handle different response formats
    if (response?.data?.data) {
      console.log('🔄 [Page Saga] Found data.data property - Standard Strapi format');
      const validatedPageData = validatePageContent(response.data.data);
      console.log('🔄 [Page Saga] Page data:', {
        id: validatedPageData.id,
        documentId: validatedPageData.documentId,
        title: validatedPageData.title,
        status: validatedPageData.status,
        hasContent: !!validatedPageData.content,
        contentValid: validatedPageData.content !== null,
        hasReferrerLink: !!(validatedPageData as any).referrer_link,
        hasAuthor: !!validatedPageData.author,
        authorId: validatedPageData.author?.id,
        authorStructure: validatedPageData.author
      });
      yield put(actions.fetchPageSuccess(validatedPageData));

      // If page has a linked referrer_link, set cookies and trigger view tracking
      if ((validatedPageData as any).referrer_link) {
        console.log('🔄 [Page Saga] Page has linked referrer_link, setting cookies and triggering view tracking');

        const referrerLink = (validatedPageData as any).referrer_link;
        const referrer = typeof window !== 'undefined' ? window.location.href : undefined;

        // Set referrer cookies if referrer data is available and not already set
        if (referrerLink.referrer?.referral_code) {
          // Import cookie utilities dynamically to avoid SSR issues
          const { setReferralData, getReferralCookie } = yield import('@/utils/cookies');

          // Check if referral cookie is already set
          const existingReferralCode = getReferralCookie();

          if (!existingReferralCode) {
            console.log('🔄 [Page Saga] Setting referrer cookies:', {
              referralCode: referrerLink.referrer.referral_code,
              referrerUrl: referrerLink.url,
              shortLink: referrerLink.short_link
            });

            // Set comprehensive referral data including short link if available
            setReferralData({
              referralCode: referrerLink.referrer.referral_code,
              referralUrl: referrerLink.url,
              shortLink: referrerLink.short_link
            });

            // Mark cookies as set in the state
            yield put(actions.markReferrerCookiesSet());
          } else {
            console.log('🔄 [Page Saga] Referral cookie already exists:', existingReferralCode);
          }
        }

        // Trigger view tracking for editor access
        yield put(actions.trackPageViewRequest({
          pageId: validatedPageData.documentId,
          sourceType: 'direct',
          referrer
        }));
      }
    } else if (response?.data && response.data.id) {
      console.log('🔄 [Page Saga] Direct page data format');
      const validatedPageData = validatePageContent(response.data);
      console.log('🔄 [Page Saga] Page data:', {
        id: validatedPageData.id,
        documentId: validatedPageData.documentId,
        title: validatedPageData.title,
        status: validatedPageData.status,
        hasContent: !!validatedPageData.content,
        contentValid: validatedPageData.content !== null,
        hasReferrerLink: !!(validatedPageData as any).referrer_link,
        hasAuthor: !!validatedPageData.author,
        authorId: validatedPageData.author?.id,
        authorStructure: validatedPageData.author
      });
      yield put(actions.fetchPageSuccess(validatedPageData));

      // If page has a linked referrer_link, set cookies and trigger view tracking
      if ((validatedPageData as any).referrer_link) {
        console.log('🔄 [Page Saga] Page has linked referrer_link, setting cookies and triggering view tracking');

        const referrerLink = (validatedPageData as any).referrer_link;
        const referrer = typeof window !== 'undefined' ? window.location.href : undefined;

        // Set referrer cookies if referrer data is available and not already set
        if (referrerLink.referrer?.referral_code) {
          // Import cookie utilities dynamically to avoid SSR issues
          const { setReferralData, getReferralCookie } = yield import('@/utils/cookies');

          // Check if referral cookie is already set
          const existingReferralCode = getReferralCookie();

          if (!existingReferralCode) {
            console.log('🔄 [Page Saga] Setting referrer cookies:', {
              referralCode: referrerLink.referrer.referral_code,
              referrerUrl: referrerLink.url,
              shortLink: referrerLink.short_link
            });

            // Set comprehensive referral data including short link if available
            setReferralData({
              referralCode: referrerLink.referrer.referral_code,
              referralUrl: referrerLink.url,
              shortLink: referrerLink.short_link
            });

            // Mark cookies as set in the state
            yield put(actions.markReferrerCookiesSet());
          } else {
            console.log('🔄 [Page Saga] Referral cookie already exists:', existingReferralCode);
          }
        }

        // Trigger view tracking for editor access
        yield put(actions.trackPageViewRequest({
          pageId: validatedPageData.documentId,
          sourceType: 'direct',
          referrer
        }));
      }
    } else {
      console.error('🔄 [Page Saga] Invalid response format:', response?.data);
      yield put(actions.fetchPageFailure('Page not found'));
    }
  } catch (error: any) {
    console.error('🔄 [Page Saga] Error fetching page:', error);
    console.error('🔄 [Page Saga] Error details:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
    });

    // Handle specific error cases for anonymous users
    let errorMessage;
    if (error.response?.status === 403) {
      errorMessage = 'This page is not publicly available';
    } else if (error.response?.status === 404) {
      errorMessage = 'Page not found';
    } else {
      errorMessage = yield call(handleAxiosError, error);
    }

    yield put(actions.fetchPageFailure(errorMessage));
  }
}

// Create page saga
function* handleCreatePage(
  action: PayloadAction<ICreatePageData>
): Generator<any, void, any> {
  try {
    const pageData = action.payload;
    
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.createPageFailure('Authentication required'));
      return;
    }
    
    // Call the Next.js API route
    const response = yield call(axios.post, '/api/pages', 
      { data: pageData },
      {
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: API_TIMEOUT
      }
    );
    
    if (response?.data?.documentId) {
      const newPage = response.data;
      console.log('📄 [Page Saga] Page created successfully:', newPage);

      yield put(actions.createPageSuccess(newPage));

      // Navigate to the editor for the newly created page
      if (newPage.documentId) {
        console.log('📄 [Page Saga] Navigating to editor:', `/editor/${newPage.documentId}`);

        // Use setTimeout to ensure the navigation happens after the state update
        setTimeout(() => {
          Router.push(`/editor/${newPage.documentId}`);
        }, 100);
      } else {
        console.error('📄 [Page Saga] No documentId found in created page:', newPage);
      }
    } else {
      yield put(actions.createPageFailure('Failed to create page'));
    }
  } catch (error: any) {
    const errorMessage = yield call(handleAxiosError, error);
    yield put(actions.createPageFailure(errorMessage));
  }
}

// Update page saga
function* handleUpdatePage(
  action: PayloadAction<{ id: string; data: IUpdatePageData; shouldPublishAfterSave?: boolean }>
): Generator<any, void, any> {
  try {
    const { id, data, shouldPublishAfterSave } = action.payload;

    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.updatePageFailure('Authentication required'));
      return;
    }

    // Call the Next.js API route
    const response = yield call(axios.put, `/api/pages/${id}`,
      { data },
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: API_TIMEOUT
      }
    );

    console.log("📝 [Page Saga] Update page response received:", {
      hasResponse: !!response,
      hasData: !!response?.data,
      hasNestedData: !!response?.data?.data,
      responseKeys: response?.data ? Object.keys(response.data) : [],
      nestedDataKeys: response?.data?.data ? Object.keys(response.data.data) : [],
      fullResponse: response
    });

    // Extract the actual page data from Strapi response structure
    let pageData = null;
    if (response?.data?.data) {
      // Standard Strapi format with nested data
      pageData = response.data.data;
      console.log("📝 [Page Saga] Using nested data format:", {
        hasAuthor: !!pageData.author,
        authorStructure: pageData.author,
        pageDataKeys: Object.keys(pageData),
        fullPageData: pageData
      });
    } else if (response?.data) {
      // Direct data format
      pageData = response.data;
      console.log("📝 [Page Saga] Using direct data format:", {
        hasAuthor: !!pageData.author,
        authorStructure: pageData.author,
        pageDataKeys: Object.keys(pageData),
        fullPageData: pageData
      });
    }

    if (pageData) {
      console.log("📝 [Page Saga] Dispatching updatePageSuccess with:", {
        id: pageData.id,
        documentId: pageData.documentId,
        title: pageData.title,
        status: pageData.status,
        hasContent: !!pageData.content,
        hasAuthor: !!pageData.author,
        authorId: pageData.author?.id
      });

      yield put(actions.updatePageSuccess(pageData));

      // If shouldPublishAfterSave flag is set, trigger publish after successful save
      if (shouldPublishAfterSave) {
        console.log('📝 [Page Saga] Auto-save completed, now publishing...');
        yield put(actions.publishPageRequest(id));
      }
    } else {
      console.error("📝 [Page Saga] No valid page data in response:", response);
      yield put(actions.updatePageFailure('Failed to update page - invalid response format'));
    }
  } catch (error: any) {
    const errorMessage = yield call(handleAxiosError, error);
    yield put(actions.updatePageFailure(errorMessage));
  }
}

// Auto-save page saga
function* handleAutoSavePage(
  action: PayloadAction<{ id: string; content: any }>
): Generator<any, void, any> {
  try {
    const { id, content } = action.payload;
    
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.autoSavePageFailure('Authentication required'));
      return;
    }
    
    // Call the Next.js API route for auto-save
    const response = yield call(axios.post, `/api/pages/auto-save/${id}`, 
      { content },
      {
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: API_TIMEOUT
      }
    );
    
    if (response?.data?.data) {
      yield put(actions.autoSavePageSuccess(response.data.data));
    } else {
      yield put(actions.autoSavePageFailure('Failed to auto-save page'));
    }
  } catch (error: any) {
    const errorMessage = yield call(handleAxiosError, error);
    yield put(actions.autoSavePageFailure(errorMessage));
  }
}

// Publish page saga
function* handlePublishPage(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    const pageId = action.payload;
    
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.publishPageFailure('Authentication required'));
      return;
    }
    
    // Call the Next.js API route
    const response = yield call(axios.post, `/api/pages/publish/${pageId}`, 
      {},
      {
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: API_TIMEOUT
      }
    );
    
    console.log("📝 [Page Saga] Publish page response received:", response);

    // Extract the actual page data from Strapi response structure
    let pageData = null;
    if (response?.data?.data) {
      // Standard Strapi format with nested data
      pageData = response.data.data;
      console.log("📝 [Page Saga] Using nested data format:", pageData);
    } else if (response?.data) {
      // Direct data format
      pageData = response.data;
      console.log("📝 [Page Saga] Using direct data format:", pageData);
    }

    if (pageData) {
      console.log("📝 [Page Saga] Dispatching publishPageSuccess with:", {
        id: pageData.id,
        documentId: pageData.documentId,
        title: pageData.title,
        status: pageData.status,
        hasContent: !!pageData.content,
        hasAuthor: !!pageData.author,
        authorId: pageData.author?.id
      });

      yield put(actions.publishPageSuccess(pageData));
    } else {
      console.error("📝 [Page Saga] No valid page data in response:", response);
      yield put(actions.publishPageFailure('Failed to publish page - invalid response format'));
    }
  } catch (error: any) {
    const errorMessage = yield call(handleAxiosError, error);
    yield put(actions.publishPageFailure(errorMessage));
  }
}

// Delete page saga
function* handleDeletePage(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    const pageId = action.payload;
    
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.deletePageFailure('Authentication required'));
      return;
    }
    
    // Call the Next.js API route
    const response = yield call(axios.delete, `/api/pages/${pageId}`, {
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT
    });
    
    if (response?.data?.deleted) {
      yield put(actions.deletePageSuccess(pageId));
    } else {
      yield put(actions.deletePageFailure('Failed to delete page'));
    }
  } catch (error: any) {
    const errorMessage = yield call(handleAxiosError, error);
    yield put(actions.deletePageFailure(errorMessage));
  }
}

// Fetch available pages for referrer-link saga
function* handleFetchAvailablePagesForReferrerLink(): Generator<any, void, any> {
  try {
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.fetchAvailablePagesForReferrerLinkFailure('Authentication required'));
      return;
    }

    // Call the Next.js API route
    const response = yield call(axios.get, '/api/pages/available-for-referrer-link', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT
    });

    // Handle response format
    if (response?.data?.data && Array.isArray(response.data.data)) {
      yield put(actions.fetchAvailablePagesForReferrerLinkSuccess(response.data.data));
    } else if (response?.data && Array.isArray(response.data)) {
      yield put(actions.fetchAvailablePagesForReferrerLinkSuccess(response.data));
    } else {
      console.error('Unexpected response format for available pages:', response?.data);
      yield put(actions.fetchAvailablePagesForReferrerLinkFailure('Unexpected response format'));
    }
  } catch (error: any) {
    const errorMessage = yield call(handleAxiosError, error);
    yield put(actions.fetchAvailablePagesForReferrerLinkFailure(errorMessage));
  }
}

// Track page view saga
function* handleTrackPageView(
  action: PayloadAction<{ pageId: string; referrer?: string; sourceType?: string }>
): Generator<any, void, any> {
  try {
    const { pageId, referrer, sourceType = 'direct' } = action.payload;

    // Get auth token from localStorage (optional for tracking)
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;

    // Get referral data from cookies to include in tracking
    let referralCode = null;
    let referralUrl = null;
    let shortLink = null;

    if (typeof window !== 'undefined') {
      try {
        const { getAllReferralData } = yield import('@/utils/cookies');
        const referralData = getAllReferralData();
        referralCode = referralData.referralCode;
        referralUrl = referralData.referralUrl;
        shortLink = referralData.shortLink;

        console.log('🔄 [Track Page View] Using referral data from cookies:', {
          referralCode,
          referralUrl,
          shortLink,
          sourceType,
          pageId
        });
      } catch (cookieError) {
        console.warn('🔄 [Track Page View] Could not read referral cookies:', cookieError);
      }
    }

    // Call the Next.js API route with enhanced referral data
    const response = yield call(axios.post, `/api/pages/track-view/${pageId}`,
      {
        referrer: referrer || referralUrl, // Use referral URL from cookie if no explicit referrer
        source_type: sourceType,
        referral_code: referralCode, // Include referral code for better tracking
        short_link: shortLink // Include short link for priority tracking
      },
      {
        headers: {
          ...(token && { 'Authorization': `Bearer ${token}` }),
          'Content-Type': 'application/json'
        },
        timeout: API_TIMEOUT
      }
    );

    if (response?.data?.success) {
      yield put(actions.trackPageViewSuccess({
        pageId,
        sourceType,
        referrer: referrer || null
      }));
    } else {
      yield put(actions.trackPageViewFailure('Failed to track page view'));
    }
  } catch (error: any) {
    // Silent failure for tracking - don't show errors to user
    const errorMessage = yield call(handleAxiosError, error);
    yield put(actions.trackPageViewFailure(errorMessage));
  }
}
// Image upload saga
function* handleUploadImage(
  action: PayloadAction<{ file: File; pageId: string }>
): Generator<any, void, any> {
  try {
    const { file, pageId } = action.payload;

    console.log('🔄 [Image Upload Saga] Starting image upload:', {
      fileName: file.name,
      fileSize: file.size,
      pageId,
    });

    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    console.log('🔄 [Image Upload Saga] Auth token check:', {
      hasToken: !!token,
      tokenPrefix: token ? token.substring(0, 10) + '...' : 'No token',
      isClient: typeof window !== 'undefined',
    });

    if (!token) {
      console.log('🔄 [Image Upload Saga] No auth token - failing request');
      yield put(actions.uploadImageFailure('Authentication required'));
      return;
    }

    // Validate file type and size (client-side validation)
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      yield put(actions.uploadImageFailure('Invalid file type. Only JPG, PNG, GIF, and WebP images are allowed.'));
      return;
    }

    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      yield put(actions.uploadImageFailure('File size too large. Maximum size is 5MB.'));
      return;
    }

    // Create FormData for the upload
    const formData = new FormData();
    formData.append('image', file);
    formData.append('pageId', pageId);

    // Make API request to Next.js proxy endpoint
    console.log('🔄 [Image Upload Saga] Making API call to:', '/api/pages/upload-image');
    const response = yield call(axios.post, '/api/pages/upload-image', formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        // Note: Don't set Content-Type for FormData, axios handles it automatically
      },
      timeout: API_TIMEOUT,
    });

    console.log('🔄 [Image Upload Saga] Upload successful:', {
      s3Url: response.data.s3Url,
      fileName: response.data.fileName,
      fileKey: response.data.fileKey,
    });

    // Dispatch success action with the S3 URL
    yield put(actions.uploadImageSuccess({
      s3Url: response.data.s3Url,
      fileName: response.data.fileName,
      fileKey: response.data.fileKey,
    }));

    // Dispatch custom event to notify the YooptaEditor component
    if (typeof window !== 'undefined') {
      const event = new CustomEvent('imageUploadSuccess', {
        detail: { s3Url: response.data.s3Url }
      });
      window.dispatchEvent(event);
    }

  } catch (error: any) {
    console.error('🔄 [Image Upload Saga] Upload failed:', error);

    // Handle specific error cases
    let errorMessage = 'Failed to upload image';

    if (axios.isAxiosError(error)) {
      if (error.response?.status === 413) {
        errorMessage = 'File too large. Maximum size is 5MB.';
      } else if (error.response?.status === 400) {
        errorMessage = error.response.data?.error || 'Invalid request';
      } else if (error.response?.status === 403) {
        errorMessage = 'You do not have permission to upload images for this page';
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      }
    }

    yield put(actions.uploadImageFailure(errorMessage));
  }
}

// Root saga
export function* pageSaga() {
  yield takeLatest(actions.fetchPagesRequest.type, handleFetchPages);
  yield takeLatest(actions.fetchPageRequest.type, handleFetchPage);
  yield takeLatest(actions.createPageRequest.type, handleCreatePage);
  yield takeLatest(actions.updatePageRequest.type, handleUpdatePage);
  yield takeLatest(actions.autoSavePageRequest.type, handleAutoSavePage);
  yield takeLatest(actions.publishPageRequest.type, handlePublishPage);
  yield takeLatest(actions.deletePageRequest.type, handleDeletePage);
  yield takeLatest(actions.fetchAvailablePagesForReferrerLinkRequest.type, handleFetchAvailablePagesForReferrerLink);
  yield takeLatest(actions.trackPageViewRequest.type, handleTrackPageView);
  yield takeLatest(actions.uploadImageRequest.type, handleUploadImage);
}
