import { call, put, takeLatest, select } from "redux-saga/effects";
import { actions } from "./discourse.slice";
import { authStorage } from "@/features/auth/auth.slice";
import { selectIsAuthenticated } from "@/features/auth/auth.slice";

// Safe browser check
const isBrowser = typeof window !== "undefined";

// API request utility function for Discourse middleware
const apiDiscourseRequest = async (action: string, data: any) => {
  const response = await fetch("/api/discourse", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      action,
      ...data,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "Discourse API request failed");
  }

  return response.json();
};

// Handle getting Discourse login URL (forward flow)
function* handleGetDiscourseSSOUrl(action: any): Generator<any, void, any> {
  try {
    yield put(actions.setLoading(true));

    // Extract SSO parameters from action payload if provided
    const { sso, sig } = action.payload || {};

    if (sso && sig) {
      // This is reverse flow - we have SSO parameters from Discourse
      yield call(handleReverseSSO, sso, sig);
    } else {
      // This is forward flow - redirect user to Discourse login
      yield call(handleForwardSSO);
    }
  } catch (error: any) {
    console.error("Error handling Discourse SSO:", error);
    let errorMessage = "Failed to access community. Please try again.";

    if (error.message.includes("Authentication")) {
      errorMessage = "Please sign in to access the community.";
    }

    yield put(actions.setError(errorMessage));
  } finally {
    yield put(actions.setLoading(false));
  }
}

// Handle forward SSO flow (user clicks Community button)
function* handleForwardSSO(): Generator<any, void, any> {
  console.log("Forward SSO: Getting Discourse login URL...");

  // Get the direct login URL from Discourse
  const response = yield call(apiDiscourseRequest, "get-login-url", {});

  console.log("Discourse login URL response:", response);

  if (response?.success && response?.data?.loginUrl) {
    // Set the login URL in state
    yield put(actions.setDiscourseSSOUrl(response.data.loginUrl));

    // Redirect user to Discourse login page
    if (isBrowser) {
      window.open(response.data.loginUrl, "_blank");
    }
  } else {
    throw new Error("Failed to get Discourse login URL");
  }
}

// Handle reverse SSO flow (user came from Discourse)
function* handleReverseSSO(sso: string, sig: string): Generator<any, void, any> {
  console.log("Reverse SSO: Processing SSO parameters from Discourse...");

  // Check if user is authenticated
  const isAuthenticated = yield select(selectIsAuthenticated);
  if (!isAuthenticated) {
    throw new Error("Authentication required to complete SSO");
  }

  // Get token from storage
  const token = authStorage.getStoredAuthToken();
  if (!token) {
    throw new Error("Authentication token not found");
  }

  // Process SSO parameters with authenticated user
  const response = yield call(apiDiscourseRequest, "process-sso-params", {
    token,
    sso,
    sig,
  });

  console.log("Discourse SSO response:", response);

  if (response?.success && response?.data?.ssoUrl) {
    // Set the SSO URL in state
    yield put(actions.setDiscourseSSOUrl(response.data.ssoUrl));

    // Redirect to Discourse with authenticated user data
    if (isBrowser) {
      window.location.href = response.data.ssoUrl;
    }
  } else {
    throw new Error("Failed to process SSO parameters");
  }
}

// Handle getting Discourse configuration
function* handleGetDiscourseConfig(): Generator<any, void, any> {
  try {
    yield put(actions.setLoading(true));

    console.log("Getting Discourse configuration...");

    // Use API middleware to get config
    const response = yield call(apiDiscourseRequest, "get-config", {});

    console.log("Discourse config response:", response);

    if (response?.success && response?.data) {
      // Set the configuration in state
      yield put(actions.setDiscourseConfig({
        discourseUrl: response.data.discourseUrl,
        communityName: response.data.communityName || "Community",
        ssoEnabled: response.data.ssoEnabled || false,
      }));
    } else {
      console.warn("Invalid config response from server");
    }
  } catch (error: any) {
    console.error("Error getting Discourse config:", error);
    // Don't show error for config fetch as it's not critical
    console.warn("Failed to fetch Discourse configuration");
  } finally {
    yield put(actions.setLoading(false));
  }
}

export default function* discourseSaga() {
  yield takeLatest(actions.getDiscourseSSOUrl.type, handleGetDiscourseSSOUrl);
  yield takeLatest(actions.getDiscourseConfig.type, handleGetDiscourseConfig);
}
