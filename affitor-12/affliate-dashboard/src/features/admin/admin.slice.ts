import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface Partner {
  id: number;
  documentId: string;
  referral_code: string;
  referrer_status: string;
  createdAt: string;
  user: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
    username: string;
  };
  referrer_links: { count: number };
  referrals: { count: number };
  referral_commissions: { count: number };
  payouts: { count: number };
}

interface PartnerPaymentInfo {
  paypal_email?: string;
  bank_transfer?: {
    first_name?: string;
    last_name?: string;
    business_name?: string;
    country?: string;
    city?: string;
    state?: string;
    address?: string;
    zip_code?: string;
    account_number?: string;
    swift_code?: string;
  };
}

interface PartnerPaymentInfoResponse {
  partnerId: string;
  paymentInfo: PartnerPaymentInfo;
  hasPaymentInfo: boolean;
  availableMethods: string[];
}

export interface MonthlyPerformanceData {
  month: string;
  revenue: number;
  referrals: number;
  clicks: number;
  leads: number;
}

interface DashboardStats {
  totalReferrers: number;
  totalReferrals: number;
  totalRevenue: number;
  currentMonthRevenue: number;
  previousMonthRevenueAmount: number;
  growthRate: number;
  totalClicks: number;
  totalLeads: number;
  monthlyPerformance: MonthlyPerformanceData[];
  recentActivity: Array<{
    id: number;
    documentId: string;
    amount_paid: number | null;
    referral_status: string;
    description: string;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    locale: string | null;
  }>;
  recentCustomers: Array<{
    id: number;
    documentId: string;
    referral_status: string;
    total_paid: number;
    total_commission: number;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    user: {
      id: number;
      documentId: string;
      username: string;
      email: string;
      first_name: string;
      last_name: string;
    } | null;
    referrer: {
      id: number;
      documentId: string;
      referral_code: string;
      user: {
        first_name: string;
        last_name: string;
        email: string;
        username: string;
      } | null;
    } | null;
  }>;
  topReferrers: Array<{
    id: number;
    documentId: string;
    referral_code: string;
    referrer_status: string;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    locale: string | null;
    balance: number | null;
    total_earnings: number | null;
    user: {
      id: number;
      documentId: string;
      username: string;
      email: string;
    } | null;
    totalClicks: number;
    totalLeads: number;
    totalConversions: number;
    totalCustomers: number;
    totalEarnings: number;
    totalRevenue: number;
    commissionStats: {
      totalCommissions: number;
      pendingCommissions: number;
      readyCommissions: number;
      paidCommissions: number;
      totalEarnings: number;
      totalRevenue: number;
      pendingEarnings: number;
      readyEarnings: number;
      paidEarnings: number;
    };
  }>;
  topReferrals: Array<{
    id: number;
    documentId: string;
    referral_status: string;
    total_paid: number;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    locale: string | null;
    user: {
      id: number;
      documentId: string;
      username: string;
      email: string;
    } | null;
  }>;
}

interface ProgramSettings {
  payoutCycle: string;
  minimumPayout: number;
  processingFee: number;
  reserveRate: number;
  cookieDuration: number;
  paymentMethods: {
    paypal: boolean;
    bankTransfer: boolean;
  };
}

interface AdminState {
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  adminData: any | null;
  token: string | null;
  dashboardStats: {
    data: DashboardStats | null;
    loading: boolean;
    error: string | null;
  };
  partners: {
    data: Partner[];
    loading: boolean;
    error: string | null;
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
  partnerPaymentInfo: {
    data: PartnerPaymentInfoResponse | null;
    loading: boolean;
    error: string | null;
  };
  settings: {
    data: ProgramSettings | null;
    loading: boolean;
    updating: boolean;
    error: string | null;
  };
}

const initialState: AdminState = {
  isAuthenticated: false,
  loading: false,
  error: null,
  adminData: null,
  token: null,
  dashboardStats: {
    data: null,
    loading: false,
    error: null,
  },
  partners: {
    data: [],
    loading: false,
    error: null,
    pagination: {
      page: 1,
      pageSize: 10,
      pageCount: 0,
      total: 0,
    },
  },
  partnerPaymentInfo: {
    data: null,
    loading: false,
    error: null,
  },
  settings: {
    data: null,
    loading: false,
    updating: false,
    error: null,
  },
};

const adminSlice = createSlice({
  name: "admin",
  initialState,
  reducers: {
    login: (
      state,
      action: PayloadAction<{ email: string; password: string }>
    ) => {
      state.loading = true;
      state.error = null;
    },
    loginSuccess: (
      state,
      action: PayloadAction<{ admin: any; token: string }>
    ) => {
      state.loading = false;
      state.isAuthenticated = true;
      state.adminData = action.payload.admin;
      state.token = action.payload.token;
      state.error = null;
    },
    loginFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.isAuthenticated = false;
      state.adminData = null;
      state.token = null;
      state.error = action.payload;
    },
    logout: (state) => {
      state.isAuthenticated = false;
      state.adminData = null;
      state.token = null;
      state.error = null;
      state.loading = false;
    },
    clearError: (state) => {
      state.error = null;
    },
    checkAuthStatus: (state) => {
      state.loading = true;
      state.error = null;
    },
    checkAuthStatusSuccess: (
      state,
      action: PayloadAction<{ admin: any; token: string }>
    ) => {
      state.loading = false;
      state.isAuthenticated = true;
      state.adminData = action.payload.admin;
      state.token = action.payload.token;
      state.error = null;
    },
    checkAuthStatusFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.isAuthenticated = false;
      state.adminData = null;
      state.token = null;
      state.error = action.payload;
    },
    fetchPartners: (
      state,
      action: PayloadAction<{
        page?: number;
        pageSize?: number;
        search?: string;
        status?: string;
        sort?: string;
        username?: string;
        email?: string;
        createdAtFrom?: string;
        createdAtTo?: string;
        revenueFrom?: string;
        revenueTo?: string;
        commissionFrom?: string;
        commissionTo?: string;
        clicksFrom?: string;
        clicksTo?: string;
        leadsFrom?: string;
        leadsTo?: string;
        conversionsFrom?: string;
        conversionsTo?: string;
      }>
    ) => {
      state.partners.loading = true;
      state.partners.error = null;
    },
    fetchPartnersSuccess: (
      state,
      action: PayloadAction<{ results: Partner[]; pagination: any }>
    ) => {
      state.partners.loading = false;
      state.partners.data = action.payload.results;
      state.partners.pagination = action.payload.pagination;
      state.partners.error = null;
    },
    fetchPartnersFailure: (state, action: PayloadAction<string>) => {
      state.partners.loading = false;
      state.partners.error = action.payload;
    },
    fetchPartnerPaymentInfo: (state, action: PayloadAction<string>) => {
      state.partnerPaymentInfo.loading = true;
      state.partnerPaymentInfo.error = null;
    },
    fetchPartnerPaymentInfoSuccess: (
      state,
      action: PayloadAction<PartnerPaymentInfoResponse>
    ) => {
      state.partnerPaymentInfo.loading = false;
      state.partnerPaymentInfo.data = action.payload;
      state.partnerPaymentInfo.error = null;
    },
    fetchPartnerPaymentInfoFailure: (state, action: PayloadAction<string>) => {
      state.partnerPaymentInfo.loading = false;
      state.partnerPaymentInfo.error = action.payload;
    },
    clearPartnerPaymentInfo: (state) => {
      state.partnerPaymentInfo.data = null;
      state.partnerPaymentInfo.loading = false;
      state.partnerPaymentInfo.error = null;
    },
    fetchDashboardStats: (state) => {
      state.dashboardStats.loading = true;
      state.dashboardStats.error = null;
    },
    fetchDashboardStatsSuccess: (
      state,
      action: PayloadAction<DashboardStats>
    ) => {
      state.dashboardStats.loading = false;
      state.dashboardStats.data = action.payload;
      state.dashboardStats.error = null;
    },
    fetchDashboardStatsFailure: (state, action: PayloadAction<string>) => {
      state.dashboardStats.loading = false;
      state.dashboardStats.error = action.payload;
    },
    // Settings actions
    fetchSettings: (state) => {
      state.settings.loading = true;
      state.settings.error = null;
    },
    fetchSettingsSuccess: (state, action: PayloadAction<ProgramSettings>) => {
      state.settings.loading = false;
      state.settings.data = action.payload;
      state.settings.error = null;
    },
    fetchSettingsFailure: (state, action: PayloadAction<string>) => {
      state.settings.loading = false;
      state.settings.error = action.payload;
    },
    updateSettings: (state, action: PayloadAction<ProgramSettings>) => {
      state.settings.updating = true;
      state.settings.error = null;
    },
    updateSettingsSuccess: (state, action: PayloadAction<ProgramSettings>) => {
      state.settings.updating = false;
      state.settings.data = action.payload;
      state.settings.error = null;
    },
    updateSettingsFailure: (state, action: PayloadAction<string>) => {
      state.settings.updating = false;
      state.settings.error = action.payload;
    },
  },
});

export const { actions } = adminSlice;
export const selectAdminIsAuthenticated = (state: any) =>
  state.admin?.isAuthenticated || false;
export const selectAdminLoading = (state: any) => state.admin?.loading || false;
export const selectAdminError = (state: any) => state.admin?.error || null;
export const selectAdminData = (state: any) => state.admin?.adminData || null;
export const selectAdminToken = (state: any) => state.admin?.token || null;
export const selectAdminPartners = (state: any) =>
  state.admin?.partners?.data || [];
export const selectAdminPartnersLoading = (state: any) =>
  state.admin?.partners?.loading || false;
export const selectAdminPartnersError = (state: any) =>
  state.admin?.partners?.error || null;
export const selectAdminPartnersPagination = (state: any) =>
  state.admin?.partners?.pagination || null;
export const selectAdminPartnerPaymentInfo = (state: any) =>
  state.admin?.partnerPaymentInfo?.data || null;
export const selectAdminPartnerPaymentInfoLoading = (state: any) =>
  state.admin?.partnerPaymentInfo?.loading || false;
export const selectAdminPartnerPaymentInfoError = (state: any) =>
  state.admin?.partnerPaymentInfo?.error || null;
export const selectAdminDashboardStats = (state: any) =>
  state.admin?.dashboardStats?.data || null;
export const selectAdminDashboardStatsLoading = (state: any) =>
  state.admin?.dashboardStats?.loading || false;
export const selectAdminDashboardStatsError = (state: any) =>
  state.admin?.dashboardStats?.error || null;
export const selectAdminSettings = (state: any) =>
  state.admin?.settings?.data || null;
export const selectAdminSettingsLoading = (state: any) =>
  state.admin?.settings?.loading || false;
export const selectAdminSettingsUpdating = (state: any) =>
  state.admin?.settings?.updating || false;
export const selectAdminSettingsError = (state: any) =>
  state.admin?.settings?.error || null;

export default adminSlice.reducer;
