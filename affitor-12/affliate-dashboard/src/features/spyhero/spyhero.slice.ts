import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { createSelector } from "reselect";
import { RootState } from "@/store";

export interface SpyHeroAd {
  id: string;
  platform: string;
  network: string;
  crawledAt: string;
  niche?: string;
  objective?: string;
  lastSeen?: string;
  ctr?: string;
  likes?: string;
  shares?: string;
  comments?: string;
  language?: string;
  countries?: string[];
  videoUrl?: string;
  duration?: string;
  dateRange?: string;
  runDuration?: string;
  brandName?: string;
  description?: string;
  adCopy?: string;
  thumbnail?: string;
  thumbnailUrl?: string;
  title?: string;
  headline?: string;
  videoId?: string;
  adDuration?: string;
  views?: string;
  estimatedSpend?: string;
  adSpend?: string;
  youtubeChannelUrl?: string;
  youtubeChannelId?: string;
  averageViewsPerDay?: string;
  firstSeen?: string;
  youtubeVideoId?: string;
  youtubeUrl?: string;
  totalViews?: string;
  channelName?: string;
  // Facebook-specific fields
  isVideoAd?: boolean;
  pageName?: string;
  facebookPageId?: string;
  imageUrl?: string;
  isRunning?: boolean;
  totalAdsSeen?: number;
  currentlyRunning?: number;
  daysRunning?: string;
  pageCreated?: string;
  startedRunning?: string;
}

interface SpyHeroState {
  ads: SpyHeroAd[];
  loading: boolean;
  error: string | null;
  searchKeyword: string;
  searchPlatform: string;
  totalCount: number;
  lastCrawled: string | null;
}

const initialState: SpyHeroState = {
  ads: [],
  loading: false,
  error: null,
  searchKeyword: "",
  searchPlatform: "youtube",
  totalCount: 0,
  lastCrawled: null,
};

const selectSpyHeroState = (state: RootState) => state.spyhero;

const spyheroSlice = createSlice({
  name: "spyhero",
  initialState,
  reducers: {
    // Search actions
    searchAdsRequest: (
      state,
      action: PayloadAction<{ keyword: string; platform: string }>
    ) => {
      state.loading = true;
      state.error = null;
      state.searchKeyword = action.payload.keyword;
      state.searchPlatform = action.payload.platform;
    },
    searchAdsSuccess: (
      state,
      action: PayloadAction<{
        ads: SpyHeroAd[];
        totalCount: number;
        lastCrawled: string;
      }>
    ) => {
      state.loading = false;
      state.ads = action.payload.ads;
      state.totalCount = action.payload.totalCount;
      state.lastCrawled = action.payload.lastCrawled;
      state.error = null;
    },
    searchAdsFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    // Clear results
    clearSearchResults: (state) => {
      state.ads = [];
      state.totalCount = 0;
      state.lastCrawled = null;
      state.error = null;
    },
    // Set search filters
    setSearchKeyword: (state, action: PayloadAction<string>) => {
      state.searchKeyword = action.payload;
    },
    setSearchPlatform: (state, action: PayloadAction<string>) => {
      state.searchPlatform = action.payload;
    },
    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    // Set error state
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { actions, reducer } = spyheroSlice;

// Selectors
export const selectSpyHeroAds = createSelector(
  [selectSpyHeroState],
  (spyhero) => spyhero.ads
);

export const selectSpyHeroLoading = createSelector(
  [selectSpyHeroState],
  (spyhero) => spyhero.loading
);

export const selectSpyHeroError = createSelector(
  [selectSpyHeroState],
  (spyhero) => spyhero.error
);

export const selectSpyHeroSearchKeyword = createSelector(
  [selectSpyHeroState],
  (spyhero) => spyhero.searchKeyword
);

export const selectSpyHeroSearchPlatform = createSelector(
  [selectSpyHeroState],
  (spyhero) => spyhero.searchPlatform
);

export const selectSpyHeroTotalCount = createSelector(
  [selectSpyHeroState],
  (spyhero) => spyhero.totalCount
);

export const selectSpyHeroLastCrawled = createSelector(
  [selectSpyHeroState],
  (spyhero) => spyhero.lastCrawled
);

export default reducer;
