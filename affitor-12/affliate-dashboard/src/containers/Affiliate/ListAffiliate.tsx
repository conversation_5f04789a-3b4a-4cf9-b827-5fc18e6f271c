"use client";

import { useState, useEffect, useMemo, useCallback, useRef } from "react";
import Loading from "../../components/Loading";
import { TableAffiliate } from "../../components/TableAffiliate";
import { useDispatch, useSelector } from "react-redux";
import { affiliateActions } from "@/features/rootActions";
import {
  selectAffiliatePrograms,
  selectAffiliatePagination,
  selectErrorAffiliate,
  selectLoadingAffiliatePrograms,
} from "@/features/selectors";
import {
  selectFilters,
  selectActiveTag,
  selectActiveCategory,
  selectActiveFilterCount,
  selectIsHashInitialized,
  actions as filterActions,
} from "@/features/filter/filter.slice";
import { CustomButton } from "@/components/CustomButton";
import { SortingState } from "@tanstack/react-table";

export default function ListAffiliate() {
  // Get filter state from Redux
  const filters = useSelector(selectFilters);
  const activeTag = useSelector(selectActiveTag);
  const activeCategory = useSelector(selectActiveCategory);
  const activeFilterCount = useSelector(selectActiveFilterCount);
  const isHashInitialized = useSelector(selectIsHashInitialized);

  // Get affiliate data from Redux
  const affiliatePrograms = useSelector(selectAffiliatePrograms);
  const affiliatePagination = useSelector(selectAffiliatePagination);
  const affiliateError = useSelector(selectErrorAffiliate);
  const loading = useSelector(selectLoadingAffiliatePrograms);

  // Track fetch status to prevent duplicate API calls
  const fetchInProgressRef = useRef(false);
  const hasInitializedRef = useRef(false);
  const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const dispatch = useDispatch();
  const { fetch: fetchAffiliates, setAffiliates } = affiliateActions;

  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "monthly_traffic",
      desc: true,
    },
  ]);

  // Memoize sorting to prevent unnecessary re-renders
  const memoizedSorting = useMemo(() => {
    return sorting;
  }, [sorting && sorting.length > 0 ? JSON.stringify(sorting) : "empty"]);

  const [currentPage, setCurrentPage] = useState(1);
  const [isRetrying, setIsRetrying] = useState(false);

  const handleSetSorting = useCallback(
    (value: any) => {
      dispatch(setAffiliates(null));
      setSorting(value);
    },
    [dispatch, setAffiliates]
  );

  const handleShowMoreClick = useCallback(() => {
    setCurrentPage((prev) => prev + 1);
  }, []);

  const handleClearFilters = useCallback(() => {
    // Reset all filters to default
    dispatch(filterActions.resetFilters());

    // Need to reset the page to 1 when clearing filters
    setCurrentPage(1);

    // Clear current affiliate data to show loading state
    dispatch(setAffiliates(null));

    // Refetch data with cleared filters
    dispatch(
      fetchAffiliates({
        pagination: { page: 1, pageSize: 50 },
        filters: {},
        sort: memoizedSorting.map((sort) => ({
          field: sort.id,
          order: sort.desc ? "desc" : "asc",
        })),
      })
    );
  }, [dispatch, fetchAffiliates, memoizedSorting, setAffiliates]);

  // Create a memoized fetch function that can be reused
  const fetchAffiliateData = useCallback(() => {
    // Prevent duplicate fetch calls
    if (fetchInProgressRef.current) {
      console.log("🛑 Fetch already in progress, skipping duplicate request");
      return;
    }

    fetchInProgressRef.current = true;

    // Prepare filters object for Strapi's complex filtering format
    const strapiFilters: any = {};

    // Only add base filters if they have values
    // Don't add category filter if "All Programs" is selected (empty slug)

    // Use activeCategory as primary source, fallback to filters.category only if activeCategory is not set
    if (activeCategory) {
      // If activeCategory exists, use its slug (only add filter if slug is not empty)
      if (activeCategory.slug && activeCategory.slug !== "") {
        strapiFilters.categories = {
          slug: activeCategory.slug,
        };
      }
      // If activeCategory.slug is empty, don't add any category filter (All Programs)
    } else if (filters.category && filters.category !== "") {
      // Fallback to category in filters only if activeCategory is not set
      // But only if it's not "All Programs" (empty string)
      strapiFilters.categories = {
        slug: filters.category,
      };
    }

    if (activeTag?.id) {
      strapiFilters.tags = activeTag.id;
    }

    // Add advanced filters from the global filter state
    // Define mappings for simple range filters (field name -> API field name)
    const rangeFilters = {
      pricing: "avg_price",
      conversion: "avg_conversion",
      monthlyTraffic: "monthly_traffic",
      cookiesDuration: "cookies_duration",
    };

    // Handle all simple range filters in a generic way
    Object.entries(rangeFilters).forEach(([filterKey, apiField]) => {
      const filter = filters[filterKey as keyof typeof filters] as
        | { from?: string; to?: string }
        | undefined;

      if (filter?.from || filter?.to) {
        strapiFilters[apiField] = {};

        if (filter?.from) {
          strapiFilters[apiField].$gte = Number(filter.from);
        }

        if (filter?.to) {
          strapiFilters[apiField].$lte = Number(filter.to);
        }
      }
    });

    // Handle relational filters with nesting

    // Commission filter (nested structure)
    if (filters.commission?.from || filters.commission?.to) {
      if (!strapiFilters.commission) {
        strapiFilters.commission = { avg_commission: {} };
      }

      if (filters.commission?.from) {
        strapiFilters.commission.avg_commission.$gte = Number(
          filters.commission.from
        );
      }

      if (filters.commission?.to) {
        strapiFilters.commission.avg_commission.$lte = Number(
          filters.commission.to
        );
      }
    }

    // Payment method filter (relational - many to many)
    if (filters.paymentMethod) {
      if (!strapiFilters.payment_methods) {
        strapiFilters.payment_methods = {};
      }
      strapiFilters.payment_methods.documentId = filters.paymentMethod;
    }

    // Recurring filter (boolean)
    if (filters.recurring) {
      strapiFilters.recurring = filters.recurring;
    }

    // Country filter (array of countries)
    if (filters.countries && filters.countries.length > 0) {
      strapiFilters.country = {
        $in: filters.countries
      };
    }

    // Launch Year filter (array of years)
    if (filters.launchYears && filters.launchYears.length > 0) {
      strapiFilters.launch_year = {
        $in: filters.launchYears.map(year => parseInt(year))
      };
    }

    console.log("🔍 Fetching affiliates with filters:", strapiFilters);
    console.log("📄 Requesting page:", currentPage);
    dispatch(
      fetchAffiliates({
        pagination: {
          page: currentPage,
          pageSize: 50,
        },
        filters: strapiFilters,
        sort: memoizedSorting.map((sort) => ({
          field: sort.id,
          order: sort.desc ? "desc" : "asc",
        })),
      })
    );
  }, [
    activeTag,
    activeCategory,
    currentPage,
    memoizedSorting,
    filters,
    dispatch,
    fetchAffiliates,
  ]);

  // Effect to handle filter changes (reset page to 1)
  useEffect(() => {
    if (!isHashInitialized || !hasInitializedRef.current) {
      return;
    }

    console.log("🔄 Filter changed, resetting page to 1");
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [
    isHashInitialized,
    activeTag?.id,
    activeCategory?.slug,
    filters.pricing.from,
    filters.pricing.to,
    filters.commission.from,
    filters.commission.to,
    filters.conversion.from,
    filters.conversion.to,
    filters.monthlyTraffic.from,
    filters.monthlyTraffic.to,
    filters.cookiesDuration.from,
    filters.cookiesDuration.to,
    filters.paymentMethod,
    filters.recurring,
    filters.countries,
    filters.launchYears,
    JSON.stringify(memoizedSorting),
  ]);

  // Effect to handle data fetching
  useEffect(() => {
    // If hash is not initialized yet, wait for it
    if (!isHashInitialized) {
      console.log("⏳ ListAffiliate: Waiting for hash initialization");
      return;
    }

    // Check if it's the initial load after hash initialization
    if (!hasInitializedRef.current) {
      console.log("🚀 ListAffiliate: Initial fetch after hash initialization");
      hasInitializedRef.current = true;
      fetchAffiliateData();
      return;
    }

    // After initialization, fetch when page changes
    if (hasInitializedRef.current) {
      console.log(
        "🔄 ListAffiliate: Fetching affiliate data for page",
        currentPage
      );
      fetchAffiliateData();
    }
  }, [isHashInitialized, currentPage]);

  // Handle when affiliatePrograms is set to null (data refresh needed)
  useEffect(() => {
    if (
      affiliatePrograms === null &&
      isHashInitialized &&
      hasInitializedRef.current
    ) {
      console.log("🔄 ListAffiliate: Data cleared, fetching fresh data");
      fetchAffiliateData();
    }
  }, [affiliatePrograms, isHashInitialized, fetchAffiliateData]);

  // Reset fetch lock when affiliates data arrives
  useEffect(() => {
    if (affiliatePrograms || affiliateError) {
      fetchInProgressRef.current = false;
    }
  }, [affiliatePrograms, affiliateError]);

  // Add effect to track when retry loading should end
  useEffect(() => {
    if (isRetrying && (affiliatePrograms || affiliateError)) {
      setIsRetrying(false);
    }
  }, [affiliatePrograms, affiliateError, isRetrying]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
    };
  }, []);

  return (
    <>
      {affiliateError ? (
        <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
          <div className="text-red-500 mb-3 text-5xl">
            <i className="fas fa-exclamation-circle"></i>
          </div>
          <h3 className="text-xl font-semibold mb-2">
            Error Loading Affiliates
          </h3>
          <div className="flex gap-3 mt-4">
            <>
              <button
                onClick={() => {
                  setIsRetrying(true);
                  dispatch(setAffiliates(null));
                  // Reset to page 1 when retrying after an error
                  setCurrentPage(1);
                  // Retry fetch
                  fetchAffiliateData();
                }}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
              >
                Try Again
              </button>
            </>
          </div>
        </div>
      ) : affiliatePrograms ? (
        <div className="w-full overflow-hidden">
          {affiliatePrograms.length > 0 ? (
            <>
              <div className="overflow-x-auto md:overflow-visible">
                <TableAffiliate
                  data={affiliatePrograms}
                  pageSize={50}
                  isPagination={false}
                  sorting={sorting}
                  setSorting={handleSetSorting}
                />
              </div>
              <div className="w-full flex justify-center p-4">
                {!loading ? (
                  <CustomButton
                    id="loading-more-affiliate"
                    className={`!border-0 font-medium bg-secondary ${
                      affiliatePagination?.page ===
                      affiliatePagination?.pageCount
                        ? "hidden"
                        : ""
                    }`}
                    onClick={handleShowMoreClick}
                  >
                    Show More
                  </CustomButton>
                ) : (
                  <Loading containerClassName="!h-full" />
                )}
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
              <div className="text-gray-400 mb-3 text-5xl">
                <i className="fas fa-search"></i>
              </div>
              <h3 className="text-xl font-semibold mb-2">No results found</h3>
              <p className="text-gray-500 max-w-md">
                We couldn't find any affiliate programs matching your current
                filters. Try adjusting your filter criteria or category
                selection.
              </p>
              {activeFilterCount > 0 && (
                <button
                  onClick={handleClearFilters}
                  className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center gap-2"
                >
                  <i className="fas fa-times-circle"></i>
                  Clear Filters
                </button>
              )}
            </div>
          )}
        </div>
      ) : (
        <Loading
          containerClassName="!h-full !p-5"
          className="!w-[40px] !h-[40px]"
        />
      )}
      <div className="flex justify-end"></div>
    </>
  );
}
