import React, { useEffect, useState, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useRouter } from "next/router";
import { ICategory } from "@/interfaces";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronLeftIcon, ChevronRightIcon } from "lucide-react";
import { selectCategories, selectCategoryLoading } from "@/features/selectors";
import {
  selectActiveCategory,
  selectFilters,
  actions as filterActions,
} from "@/features/filter/filter.slice";
import { affiliateActions, categoryActions } from "@/features/rootActions";

export default function FilterCategories() {
  const dispatch = useDispatch();
  const router = useRouter();
  const categories = useSelector(selectCategories);
  const activeCategory = useSelector(selectActiveCategory);
  const filters = useSelector(selectFilters);

  // Scroll state and refs
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(false);
  const isLoading = useSelector(selectCategoryLoading);

  // Load categories if needed
  useEffect(() => {
    if (categories.length <= 1) {
      console.log("FilterCategories: Fetching categories");
      dispatch(categoryActions.fetchAll());
    }
  }, [dispatch, categories.length]);

  // Check scroll state
  const checkScrollState = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } =
        scrollContainerRef.current;
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth);
    }
  };

  // Set up scroll detection
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      checkScrollState();
      container.addEventListener("scroll", checkScrollState);

      // Check on resize
      const handleResize = () => checkScrollState();
      window.addEventListener("resize", handleResize);

      return () => {
        container.removeEventListener("scroll", checkScrollState);
        window.removeEventListener("resize", handleResize);
      };
    }
  }, [categories]); // Re-run when categories change

  // Scroll functions
  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -200, behavior: "smooth" });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 200, behavior: "smooth" });
    }
  };

  // Synchronize the current category from filter state with the active category
  useEffect(() => {
    // Only synchronize if we have both loaded categories and a category slug from URL or filter
    if (categories.length > 1 && filters.category) {
      // Check if we need to update
      if (!activeCategory || activeCategory.slug !== filters.category) {
        const filterCategory = categories.find(
          (cat) => cat.slug === filters.category
        );
        if (filterCategory) {
          console.log(
            "FilterCategories: Syncing active category from filter:",
            filterCategory.name
          );
          dispatch(filterActions.setActiveCategory(filterCategory));
          dispatch(categoryActions.setCurrentCategory(filterCategory));
        }
      }
    }
  }, [categories.length, filters.category, activeCategory?.slug, dispatch]);

  // Set default "All Programs" category if nothing else is selected
  useEffect(() => {
    if (categories.length > 0 && !activeCategory && !filters.category) {
      const allProgramsCategory = categories.find(
        (cat) => cat.name === "All Programs" || cat.slug === ""
      );

      if (allProgramsCategory) {
        console.log("Setting default 'All Programs' category");
        dispatch(filterActions.setActiveCategory(allProgramsCategory));
      }
    }
  }, [categories, activeCategory, filters.category, dispatch]);

  const handleCategoryClick = (category: ICategory) => {
    // Check if clicking the already selected category using the same logic as the styling
    const isCurrentlySelected =
      activeCategory &&
      (activeCategory.slug === category.slug ||
        (activeCategory.slug === "" && category.slug === "") ||
        (activeCategory.name === "All Programs" &&
          category.name === "All Programs"));

    if (isCurrentlySelected) return;

    console.log("FilterCategories: Category clicked:", category.name);

    // Update both states to keep them in sync
    dispatch(filterActions.setActiveCategory(category));
    dispatch(categoryActions.setCurrentCategory(category));

    // Signal that affiliate data needs to be refreshed
    dispatch(affiliateActions.setAffiliates(null));

    // Navigate to the category URL instead of using hash
    const categorySlug = category.slug || "";

    // For "All Programs" category (empty slug), navigate to the base path
    if (!categorySlug) {
      // Preserve any existing non-category filters in the hash
      const currentHash = window.location.hash;
      const nonCategoryHash = currentHash.replace(/#c=[^#]*#?|#c=[^#]*$/, "");
      const cleanHash = nonCategoryHash.replace(/^#+/, "#").replace(/#+$/, "");

      router.push({
        pathname: "/",
        hash: cleanHash || undefined,
      });
    } else {
      // Navigate to category-specific path
      // Preserve any existing non-category filters in the hash
      const currentHash = window.location.hash;
      const nonCategoryHash = currentHash.replace(/#c=[^#]*#?|#c=[^#]*$/, "");
      const cleanHash = nonCategoryHash.replace(/^#+/, "#").replace(/#+$/, "");

      router.push({
        pathname: `/${categorySlug}`,
        hash: cleanHash || undefined,
      });
    }
  };

  return (
    <div className="relative mb-0 pb-0">
      {/* Left Arrow */}
      {showLeftArrow && (
        <button
          onClick={scrollLeft}
          className="absolute left-0 top-0 h-[98%] z-10 bg-background/95 px-1 flex items-center justify-center hover:bg-background shadow-md border-r border-border transition-all duration-200"
          aria-label="Scroll left"
        >
          <ChevronLeftIcon className="w-6 h-6 text-primary-foreground" />
        </button>
      )}

      {/* Right Arrow */}
      {showRightArrow && (
        <button
          onClick={scrollRight}
          className="absolute right-0 top-0 h-[98%] z-10 bg-background/95 px-1 flex items-center justify-center hover:bg-background shadow-md border-l border-border transition-all duration-200"
          aria-label="Scroll right"
        >
          <ChevronRightIcon className="w-6 h-6 text-primary-foreground" />
        </button>
      )}

      {/* Scrollable Categories Container */}
      <div
        ref={scrollContainerRef}
        className="flex overflow-x-auto border-none hide-scrollbar mb-0 pb-0"
      >
        {isLoading ? (
          <div className="flex space-x-2 p-2 w-full">
            {[1, 2, 3, 4, 5].map((i) => (
              <Skeleton key={`skeleton-${i}`} className="h-8 w-24 rounded-md" />
            ))}
          </div>
        ) : (
          categories.map((category: ICategory) => (
            <button
              key={`cat-${category.documentId || category.id}`}
              onClick={() => handleCategoryClick(category)}
              className={`whitespace-nowrap relative px-4 py-2 cursor-pointer bg-none border-none transition-all ease-in-out duration-300 ${
                activeCategory &&
                (activeCategory.slug === category.slug ||
                  (activeCategory.slug === "" && category.slug === "") ||
                  (activeCategory.name === "All Programs" &&
                    category.name === "All Programs"))
                  ? "font-semibold text-blue-500 after:absolute after:left-0 after:right-0 after:bottom-0 after:h-[2px] after:bg-blue-500"
                  : "text-primary-foreground hover:text-primary-foreground/90"
              }`}
            >
              {category.name}
            </button>
          ))
        )}
      </div>
    </div>
  );
}
