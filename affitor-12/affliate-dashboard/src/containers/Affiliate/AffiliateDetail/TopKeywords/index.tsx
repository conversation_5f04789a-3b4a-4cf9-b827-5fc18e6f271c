import { Loading } from "@/components";
import {
  selectErrorTrafficWeb,
  selectTrafficWebList,
} from "@/features/traffic-web/traffic-web.slice";
import { useSelector } from "react-redux";
import { useMemo, useState } from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, ChevronUp } from "lucide-react";

export interface ITopKeyword {
  summary: {
    total_keywords: number;
    traffic_origin_search: number;
    avg_cpc: number;
  };
  keywords: {
    name: string;
    rank: number;
    traffic: number;
    cpc: number;
  }[];
}

export default function TopKeywords() {
  const trafficWebs = useSelector(selectTrafficWebList);
  const error = useSelector(selectErrorTrafficWeb);
  const [isExpanded, setIsExpanded] = useState(false);

  const allKeywords = trafficWebs?.[0]?.top_keyword?.keywords || [];
  const displayedKeywords = isExpanded ? allKeywords : allKeywords.slice(0, 5);
  const showButton = allKeywords.length > 5;

  return (
    <div className="p-4 bg-primary shadow rounded-lg text-[10px] md:text-[12px] h-full">
      <div className="flex justify-between relative">
        <h2 className="text-base md:text-lg font-bold mb-4 text-primary-foreground">Top Keywords</h2>
        <div className="">
          {/* <DateTimePicker title="Select time range" /> */}
        </div>
      </div>
      {trafficWebs ? (
        <div>
          {trafficWebs.length > 0 ? (
            <div>
              <TableKeywords keywords={displayedKeywords} />
              <div className="flex justify-between items-center mt-4">
                <div>
                  <KeywordsData
                    data={trafficWebs[0].top_keyword?.summary || {}}
                  />
                </div>
                {showButton && (
                  <div className="pt-4 text-center">
                    <button
                      onClick={() => setIsExpanded(!isExpanded)}
                      className="inline-flex items-center gap-1 rounded-full border border-slate-300 dark:border-slate-700 bg-transparent px-3 py-1 text-xs font-medium text-slate-600 dark:text-slate-300 transition-colors hover:bg-slate-100 dark:hover:bg-slate-800"
                    >
                      <span>{isExpanded ? "Show Less" : "Show More"}</span>
                      {isExpanded ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
                    </button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <p className="text-xs md:text-sm">Statistic data is not available</p>
          )}
        </div>
      ) : !error ? (
        <Loading containerClassName="!h-[300px]" />
      ) : null}
    </div>
  );
}

function TableKeywords({ keywords }: { keywords: ITopKeyword["keywords"] }) {
  const columns = useMemo(
    () => [
      {
        accessorKey: "rank",
        header: "Rank",
        cell: (info: any) => <span>{info.getValue()}</span>,
      },
      {
        accessorKey: "name",
        header: "Keyword",
        cell: (info: any) => <span>{info.getValue()}</span>,
      },
      {
        accessorKey: "traffic",
        header: "Traffic",
        cell: (info: any) => (
          <span>
            {info.getValue() ? info.getValue().toLocaleString() : "0"}
          </span>
        ),
      },
      {
        accessorKey: "cpc",
        header: "CPC",
        cell: (info: any) => (
          <span>
            {typeof info.getValue() === "number"
              ? `${info.getValue().toFixed(2)}$`
              : "0.00$"}
          </span>
        ),
      },
    ],
    []
  );

  const table = useReactTable({
    data: keywords,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="rounded-md border border-slate-200 dark:border-slate-800 w-full">
      <table className="w-full">
        <thead className="bg-slate-50 dark:bg-slate-800">
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  className="p-2 text-left font-medium text-slate-600 dark:text-slate-300 border-b border-slate-200 dark:border-slate-700"
                >
                  {!header.isPlaceholder &&
                    flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.length > 0 ? (
            table.getRowModel().rows.map((row) => (
              <tr key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <td className="p-2 border-b border-slate-200 dark:border-slate-800" key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={columns.length} className="text-center p-4">
                No data available.
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
}

function KeywordsData({ data }: { data: ITopKeyword["summary"] }) {
  return (
    <div className="mt-4">
      <div className="flex flex-col justify-between gap-2 text-xs md:text-sm text-slate-600 dark:text-slate-300">
        <div>
          Avg CPC: <span className="font-bold text-slate-800 dark:text-white">{data.avg_cpc?.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
}
