import { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { Loading } from '@/components';
import {
  selectErrorTrafficWeb,
  selectTrafficWebList
} from '@/features/traffic-web/traffic-web.slice';
import { getCountryISOCode } from '@/utils/countries';
import { ChevronDown, ChevronUp } from "lucide-react";

export default function Geography() {
  const trafficWebs = useSelector(selectTrafficWebList);
  const error = useSelector(selectErrorTrafficWeb);
  const [isExpanded, setIsExpanded] = useState(false);

  // Extract country data from trafficWebs if available
  const countryData = trafficWebs && 
    trafficWebs[0] && 
    trafficWebs[0]?.top_countries
      ? trafficWebs[0].top_countries
      : [];
  
  const sortedCountryData = useMemo(() => {
    return countryData.length > 0 ? [...countryData].sort((a, b) => (b.visits || 0) - (a.visits || 0)) : [];
  }, [countryData]);

  const displayedCountries = isExpanded ? sortedCountryData : sortedCountryData.slice(0, 5);
  const showButton = sortedCountryData.length > 5;

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="p-4 bg-primary shadow rounded-lg text-xs md:text-[13px] h-full">
      <h2 className="text-base md:text-lg font-bold mb-4 text-primary-foreground">Top Countries</h2>
      
      {trafficWebs ? (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-slate-50 dark:bg-slate-800">
                <th className="p-2 text-left font-medium text-slate-600 dark:text-slate-300 border-b border-slate-200 dark:border-slate-700">Flag</th>
                <th className="p-2 text-left font-medium text-slate-600 dark:text-slate-300 border-b border-slate-200 dark:border-slate-700">Country Name</th>
                <th className="p-2 text-right font-medium text-slate-600 dark:text-slate-300 border-b border-slate-200 dark:border-slate-700 w-24 md:w-32">Visits</th>
                <th className="p-2 text-right font-medium text-slate-600 dark:text-slate-300 border-b border-slate-200 dark:border-slate-700 w-24 md:w-32">Percentage</th>
              </tr>
            </thead>
            <tbody>
              {displayedCountries.length > 0 ? (
                // Sort the data by visits in descending order
                displayedCountries.map((country, index, sortedArray) => {
                    const isoCode = getCountryISOCode(country.country_name);
                    
                    return (
                      <tr key={index} className="hover:bg-slate-50 dark:hover:bg-slate-800/50 border-b border-slate-200 dark:border-slate-800">
                        <td className="p-2 w-12">
                          {isoCode !== 'xx' ? (
                            <img 
                              src={`https://flagcdn.com/24x18/${isoCode.toLowerCase()}.png`} 
                              alt={`${country.country_name} flag`}
                              className="w-6 h-auto" 
                            />
                          ) : (
                            <div className="w-6 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                          )}
                        </td>
                        <td className="p-2 text-slate-800 dark:text-slate-200">{country.country_name}</td>
                        <td className="p-2 text-right text-slate-800 dark:text-slate-200">
                          {country.visits?.toLocaleString() || 'N/A'}
                        </td>
                        <td className="p-2 text-right text-slate-800 dark:text-slate-200">
                          {country.percentage}
                        </td>
                      </tr>
                    );
                  })
              ) : (
                <tr>
                  <td colSpan={4} className="p-2 text-center text-secondary-foreground">
                    No geographical data available
                  </td>
                </tr>
              )}
            </tbody>
            {showButton && (
              <tfoot>
                <tr>
                  <td colSpan={4} className="text-center pt-4">
                    <div className="pt-4 text-center">
                      <button
                        onClick={toggleExpanded}
                        className="inline-flex items-center gap-1 rounded-full border border-slate-300 dark:border-slate-700 bg-transparent px-3 py-1 text-xs font-medium text-slate-600 dark:text-slate-300 transition-colors hover:bg-slate-100 dark:hover:bg-slate-800"
                      >
                        <span>{isExpanded ? "Show Less" : "Show More"}</span>
                        {isExpanded ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
                      </button>
                    </div>
                  </td>
                </tr>
              </tfoot>
            )}
          </table>
        </div>
      ) : (
        !error && <Loading containerClassName="!h-[300px]" />
      )}
    </div>
  );
}
