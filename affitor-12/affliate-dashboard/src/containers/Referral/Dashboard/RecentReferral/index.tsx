import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/store";
import { referralActivityActions } from "@/features/rootActions";

interface ReferralData {
  id: string;
  createdAt: string;
  description: string;
  amount: number;
  status: "Lead" | "Conversion";
}

const RecentReferral: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const dispatch = useDispatch();

  const activities = useSelector(
    (state: RootState) => state.referralActivity.activities
  );
  const loading = useSelector(
    (state: RootState) => state.referralActivity.loading
  );
  const error = useSelector((state: RootState) => state.referralActivity.error);
  const meta = useSelector((state: RootState) => state.referralActivity.meta);

  useEffect(() => {
    dispatch(
      referralActivityActions.fetchActivitiesRequest({
        pagination: { page: currentPage, pageSize: 5 },
        sort: { field: "createdAt", order: "desc" },
      })
    );
  }, [dispatch, currentPage]);

  // Transform API data to component format
  const transformedReferrals: ReferralData[] = activities.map((activity) => ({
    id: activity.documentId,
    createdAt: new Date(activity.createdAt).toLocaleDateString(),
    description: activity.description || "N/A",
    amount: activity.amount_paid || 0,
    status: activity.referral_status === "conversion" ? "Conversion" : "Lead",
  }));

  const goToPage = (page: number) => {
    if (page >= 1 && page <= (meta?.pagination.pageCount || 1)) {
      setCurrentPage(page);
    }
  };

  // if (error) {
  //   return (
  //     <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
  //       <div className="p-4 sm:p-6">
  //         <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
  //           Recent Referral Activity
  //         </h2>
  //         <div className="text-red-600 dark:text-red-400">
  //           Failed to load referral activities: {error}
  //         </div>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
      <div className="p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg sm:text-xl font-medium text-gray-900 dark:text-white">
          Recent Referral Activity
        </h2>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          Latest activity from your referral links
        </p>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 dark:bg-gray-700 text-xs text-gray-500 dark:text-gray-400">
            <tr>
              <th className="px-4 sm:px-6 py-3 text-left">Created At</th>
              <th className="px-4 sm:px-6 py-3 text-left">Description</th>
              <th className="px-4 sm:px-6 py-3 text-left">Amount Paid</th>
              <th className="px-4 sm:px-6 py-3 text-left">Status</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100 dark:divide-gray-700">
            {loading ? (
              // Loading skeleton
              Array.from({ length: 3 }).map((_, index) => (
                <tr key={`skeleton-${index}`}>
                  {Array.from({ length: 4 }).map((_, cellIndex) => (
                    <td key={`cell-${cellIndex}`} className="px-4 sm:px-6 py-4">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20 animate-pulse"></div>
                    </td>
                  ))}
                </tr>
              ))
            ) : transformedReferrals.length > 0 ? (
              transformedReferrals.map((referral) => (
                <tr
                  key={referral.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <td className="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                    {referral.createdAt}
                  </td>
                  <td className="px-4 sm:px-6 py-4 text-sm text-gray-900 dark:text-gray-200">
                    {referral.description}
                  </td>
                  <td className="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-200">
                    ${referral.amount.toFixed(2)}
                  </td>
                  <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 py-1 text-xs font-medium rounded-full ${
                        referral.status === "Conversion"
                          ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                          : "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100"
                      }`}
                    >
                      {referral.status}
                    </span>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={4}
                  className="px-4 sm:px-6 py-8 text-center text-gray-500 dark:text-gray-400"
                >
                  No referral activities found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination - Similar to Commission History */}
      {/* {meta && meta.pagination.pageCount > 1 && (
        <div className="px-4 sm:px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => goToPage(currentPage - 1)}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => goToPage(currentPage + 1)}
              disabled={currentPage >= meta.pagination.pageCount}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Showing page <span className="font-medium">{currentPage}</span> of{" "}
                <span className="font-medium">{meta.pagination.pageCount}</span>
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => goToPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
                >
                  <span className="sr-only">Previous</span>
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </button>
                {Array.from({ length: Math.min(3, meta.pagination.pageCount) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <button
                      key={page}
                      onClick={() => goToPage(page)}
                      className={`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium ${
                        currentPage === page
                          ? "text-blue-600 dark:text-blue-400"
                          : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600"
                      }`}
                    >
                      {page}
                    </button>
                  );
                })}
                <button
                  onClick={() => goToPage(currentPage + 1)}
                  disabled={currentPage >= meta.pagination.pageCount}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
                >
                  <span className="sr-only">Next</span>
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      )} */}
    </div>
  );
};

export default RecentReferral;
