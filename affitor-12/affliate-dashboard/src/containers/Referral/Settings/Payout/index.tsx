import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectUserData,
  selectUserIsUpdating,
  selectUserError,
} from "@/features/selectors";
import { actions as userActions } from "@/features/user/user.slice";
import {
  CreditCard,
  DollarSign,
  Loader2,
  CheckCircle,
  AlertCircle,
  Building2,
} from "lucide-react";

const PayoutSettings: React.FC = () => {
  const dispatch = useDispatch();
  const userData = useSelector(selectUserData);
  const isUpdating = useSelector(selectUserIsUpdating);
  const userError = useSelector(selectUserError);

  const [paypalEmail, setPaypalEmail] = useState("");
  const [bankTransfer, setBankTransfer] = useState({
    accountHolderName: "", // Chủ tài khoản
    bankName: "", // Tên ngân hàng
    stk: "", // STK - Số tài khoản
  });
  const [activePaymentMethod, setActivePaymentMethod] = useState<"paypal" | "bank_transfer">("paypal");
  const [isLoading, setIsLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Load existing payment data from user data
  useEffect(() => {
    if (userData?.paypal_email) {
      setPaypalEmail(userData.paypal_email);
      setActivePaymentMethod("paypal");
    }
    if (userData?.bank_transfer) {
      const bankData = userData.bank_transfer;
      setBankTransfer({
        accountHolderName: bankData.account_holder_name || `${bankData.first_name || ""} ${bankData.last_name || ""}`.trim(),
        bankName: bankData.bank_name || "",
        stk: bankData.stk || bankData.account_number || "",
      });
      if (!userData?.paypal_email) {
        setActivePaymentMethod("bank_transfer");
      }
    }
  }, [userData]);

  // Handle update completion
  useEffect(() => {
    if (!isUpdating && isLoading) {
      setIsLoading(false);
      if (!userError) {
        setSaveSuccess(true);
        // Hide success message after 3 seconds
        const timer = setTimeout(() => {
          setSaveSuccess(false);
        }, 3000);
        return () => clearTimeout(timer);
      }
    }
  }, [isUpdating, userError, isLoading]);

  // Handle errors
  useEffect(() => {
    if (userError && isLoading) {
      setIsLoading(false);
      setErrors({ general: userError });
    }
  }, [userError, isLoading]);

  const validatePaypalEmail = (email: string): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!email.trim()) {
      newErrors.paypalEmail = "PayPal email is required";
    } else if (!/^\S+@\S+\.\S+$/.test(email)) {
      newErrors.paypalEmail = "Please enter a valid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateBankTransfer = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!bankTransfer.accountHolderName.trim()) {
      newErrors.accountHolderName = "Chủ tài khoản là bắt buộc";
    }
    if (!bankTransfer.bankName.trim()) {
      newErrors.bankName = "Tên ngân hàng là bắt buộc";
    }
    if (!bankTransfer.stk.trim()) {
      newErrors.stk = "Số tài khoản (STK) là bắt buộc";
    } else if (!/^\d{6,20}$/.test(bankTransfer.stk.replace(/\s/g, ""))) {
      newErrors.stk = "Số tài khoản phải từ 6-20 chữ số";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSavePayment = () => {
    let isValid = false;
    let updateData: any = {};

    if (activePaymentMethod === "paypal") {
      isValid = validatePaypalEmail(paypalEmail);
      if (isValid) {
        updateData = {
          paypal_email: paypalEmail,
          bank_transfer: null, // Clear bank transfer when setting PayPal
        };
      }
    } else {
      isValid = validateBankTransfer();
      if (isValid) {
        updateData = {
          paypal_email: null, // Clear PayPal when setting bank transfer
          bank_transfer: {
            account_holder_name: bankTransfer.accountHolderName,
            bank_name: bankTransfer.bankName,
            stk: bankTransfer.stk,
          },
        };
      }
    }

    if (isValid) {
      setIsLoading(true);
      setSaveSuccess(false);
      dispatch(userActions.updateUserProfile(updateData));
    }
  };

  const handleEmailChange = (value: string) => {
    setPaypalEmail(value);
    // Clear success message when user starts editing
    if (saveSuccess) {
      setSaveSuccess(false);
    }
    // Clear errors when typing
    if (errors.paypalEmail) {
      setErrors({});
    }
  };

  const handleBankTransferChange = (field: keyof typeof bankTransfer, value: string) => {
    setBankTransfer(prev => ({
      ...prev,
      [field]: value,
    }));
    // Clear success message when user starts editing
    if (saveSuccess) {
      setSaveSuccess(false);
    }
    // Clear errors when typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  return (
    <div className="space-y-8">
      {/* Success/Error Messages */}
      {saveSuccess && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 flex items-center">
          <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mr-3" />
          <p className="text-green-700 dark:text-green-300">
            Payment settings updated successfully!
          </p>
        </div>
      )}

      {(errors.general || errors.paypalEmail || Object.keys(errors).some(key => key !== 'general' && errors[key])) && !saveSuccess && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 flex items-center">
          <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mr-3" />
          <p className="text-red-700 dark:text-red-300">
            {errors.general || errors.paypalEmail || "Please fix the errors below"}
          </p>
        </div>
      )}

      {/* Payment Method Section */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Payment Method
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Choose how you want to receive your commissions.
        </p>

        {/* Payment Method Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-600 mb-6">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActivePaymentMethod("paypal")}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activePaymentMethod === "paypal"
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"
              }`}
            >
              PayPal
            </button>
            <button
              onClick={() => setActivePaymentMethod("bank_transfer")}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activePaymentMethod === "bank_transfer"
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"
              }`}
            >
              Bank Transfer
            </button>
          </nav>
        </div>

        {/* PayPal Section */}
        {activePaymentMethod === "paypal" && (
          <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <CreditCard className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white">
                PayPal
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Receive payments via PayPal
              </p>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                PayPal Email
              </label>
              <input
                type="email"
                value={paypalEmail}
                onChange={(e) => handleEmailChange(e.target.value)}
                className={`w-full px-3 py-2 border ${
                  errors.paypalEmail
                    ? "border-red-500 dark:border-red-500"
                    : "border-gray-300 dark:border-gray-600"
                } rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500`}
                placeholder="<EMAIL>"
                disabled={isLoading || isUpdating}
              />
              {errors.paypalEmail && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.paypalEmail}
                </p>
              )}
            </div>

            <button
              onClick={handleSavePayment}
              disabled={isLoading || isUpdating || !paypalEmail.trim()}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading || isUpdating ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Saving Payment Settings...
                </>
              ) : (
                "Save Payment Settings"
              )}
            </button>
          </div>

          <div className="flex items-center mt-4 text-blue-600 bg-blue-100/50 dark:bg-blue-900/20 p-3 rounded">
            <span className="text-yellow-500 mr-2">💡</span>
            <p className="text-sm">Fastest and most secure payment method</p>
          </div>
        </div>
        )}

        {/* Bank Transfer Section */}
        {activePaymentMethod === "bank_transfer" && (
          <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <Building2 className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">
                  Chuyển khoản ngân hàng
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Nhận thanh toán qua chuyển khoản ngân hàng Việt Nam
                </p>
              </div>
            </div>

            <div className="space-y-4">
              {/* Chủ tài khoản */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Chủ tài khoản <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={bankTransfer.accountHolderName}
                  onChange={(e) => handleBankTransferChange("accountHolderName", e.target.value)}
                  className={`w-full px-3 py-2 border ${
                    errors.accountHolderName
                      ? "border-red-500 dark:border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  } rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500`}
                  placeholder="Nguyễn Văn A"
                  disabled={isLoading || isUpdating}
                />
                {errors.accountHolderName && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.accountHolderName}
                  </p>
                )}
              </div>

              {/* Tên ngân hàng */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tên ngân hàng <span className="text-red-500">*</span>
                </label>
                <select
                  value={bankTransfer.bankName}
                  onChange={(e) => handleBankTransferChange("bankName", e.target.value)}
                  className={`w-full px-3 py-2 border ${
                    errors.bankName
                      ? "border-red-500 dark:border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  } rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500`}
                  disabled={isLoading || isUpdating}
                >
                  <option value="">Chọn ngân hàng</option>

                  {/* Ngân hàng Nhà nước */}
                  <option value="Ngân hàng Nông nghiệp và Phát triển Nông thôn Việt Nam (Agribank)">Agribank - Ngân hàng Nông nghiệp và Phát triển Nông thôn Việt Nam</option>

                  {/* Ngân hàng thương mại cổ phần đại chúng */}
                  <option value="Ngân hàng TMCP Việt Nam Thịnh Vượng (VPBank)">VPBank - Ngân hàng TMCP Việt Nam Thịnh Vượng</option>
                  <option value="Ngân hàng TMCP Kỹ Thương Việt Nam (Techcombank)">Techcombank - Ngân hàng TMCP Kỹ Thương Việt Nam</option>
                  <option value="Ngân hàng Đầu tư và Phát triển Việt Nam (BIDV)">BIDV - Ngân hàng Đầu tư và Phát triển Việt Nam</option>
                  <option value="Ngân hàng Quân đội (MBBank)">MBBank - Ngân hàng Quân đội</option>
                  <option value="Ngân hàng TMCP Ngoại thương Việt Nam (Vietcombank)">Vietcombank - Ngân hàng TMCP Ngoại thương Việt Nam</option>
                  <option value="Ngân hàng Công thương Việt Nam (VietinBank)">VietinBank - Ngân hàng Công thương Việt Nam</option>
                  <option value="Ngân hàng TMCP Á Châu (ACB)">ACB - Ngân hàng TMCP Á Châu</option>
                  <option value="Ngân hàng TMCP Sài Gòn – Hà Nội (SHB)">SHB - Ngân hàng TMCP Sài Gòn – Hà Nội</option>
                  <option value="Ngân hàng TMCP Phát triển Thành phố Hồ Chí Minh (HDBank)">HDBank - Ngân hàng TMCP Phát triển TP.HCM</option>
                  <option value="Ngân hàng TMCP Lộc Phát Việt Nam (LPBank)">LPBank - Ngân hàng TMCP Lộc Phát Việt Nam</option>
                  <option value="Ngân hàng TMCP Quốc tế Việt Nam (VIB)">VIB - Ngân hàng TMCP Quốc tế Việt Nam</option>
                  <option value="Ngân hàng TMCP Đông Nam Á (SeABank)">SeABank - Ngân hàng TMCP Đông Nam Á</option>
                  <option value="Ngân hàng Tiên Phong (TPBank)">TPBank - Ngân hàng Tiên Phong</option>
                  <option value="Ngân hàng TMCP Hàng hải Việt Nam (MSB)">MSB - Ngân hàng TMCP Hàng hải Việt Nam</option>
                  <option value="Ngân hàng TMCP Xuất Nhập khẩu Việt Nam (Eximbank)">Eximbank - Ngân hàng TMCP Xuất Nhập khẩu Việt Nam</option>
                  <option value="Ngân hàng TMCP Phương Đông (OCB)">OCB - Ngân hàng TMCP Phương Đông</option>
                  <option value="Ngân hàng TMCP Kiên Long (KienLongBank)">KienLongBank - Ngân hàng TMCP Kiên Long</option>
                  <option value="Ngân hàng TMCP Quốc Dân (NCB)">NCB - Ngân hàng TMCP Quốc Dân</option>
                  <option value="Ngân hàng TMCP An Bình (ABBANK)">ABBANK - Ngân hàng TMCP An Bình</option>
                  <option value="Ngân hàng TMCP Bản Việt (Viet Capital Bank)">Viet Capital Bank - Ngân hàng TMCP Bản Việt</option>
                  <option value="Ngân hàng TMCP Việt Nam Thương Tín (VietBank)">VietBank - Ngân hàng TMCP Việt Nam Thương Tín</option>
                  <option value="Ngân hàng TMCP Việt Á (Viet A Bank)">Viet A Bank - Ngân hàng TMCP Việt Á</option>
                  <option value="Ngân hàng TMCP Bắc Á (Bac A Bank)">Bac A Bank - Ngân hàng TMCP Bắc Á</option>
                  <option value="Ngân hàng TMCP Nam Á (Nam A Bank)">Nam A Bank - Ngân hàng TMCP Nam Á</option>
                  <option value="Ngân hàng TMCP Thịnh vượng và Phát triển (PGBank)">PGBank - Ngân hàng TMCP Thịnh vượng và Phát triển</option>
                  <option value="Ngân hàng TMCP Sài Gòn Công Thương (Saigonbank)">Saigonbank - Ngân hàng TMCP Sài Gòn Công Thương</option>
                  <option value="Ngân hàng TMCP Đại Chúng Việt Nam (PVcomBank)">PVcomBank - Ngân hàng TMCP Đại Chúng Việt Nam</option>
                  <option value="Ngân hàng TMCP Sài Gòn (SCB)">SCB - Ngân hàng TMCP Sài Gòn</option>
                  <option value="Ngân hàng TMCP Bảo Việt (BaoViet Bank)">BaoViet Bank - Ngân hàng TMCP Bảo Việt</option>
                  <option value="Ngân hàng TMCP Sài Gòn Thương Tín (Sacombank)">Sacombank - Ngân hàng TMCP Sài Gòn Thương Tín</option>

                  {/* Ngân hàng 100% vốn nước ngoài */}
                  <option value="Ngân hàng TNHH một thành viên HSBC (Việt Nam)">HSBC Việt Nam</option>
                  <option value="Ngân hàng TNHH một thành viên Standard Chartered (Việt Nam)">Standard Chartered Việt Nam</option>
                  <option value="Ngân hàng Citibank Việt Nam">Citibank Việt Nam</option>
                  <option value="Ngân hàng TNHH một thành viên UOB Việt Nam">UOB Việt Nam</option>
                  <option value="Ngân hàng TNHH một thành viên Shinhan Việt Nam">Shinhan Bank Việt Nam</option>
                  <option value="Ngân hàng TNHH một thành viên Hong Leong Việt Nam">Hong Leong Bank Việt Nam</option>
                  <option value="Ngân hàng TNHH một thành viên Public Việt Nam">Public Bank Việt Nam</option>

                  {/* Ngân hàng liên doanh */}
                  <option value="Ngân hàng TNHH Indovina (IVB)">IVB - Ngân hàng TNHH Indovina</option>
                  <option value="Ngân hàng Việt - Nga (VRB)">VRB - Ngân hàng Việt - Nga</option>

                  {/* Ngân hàng khác */}
                  <option value="Khác">Khác</option>
                </select>
                {errors.bankName && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.bankName}
                  </p>
                )}
              </div>

              {/* STK - Số tài khoản */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  STK - Số tài khoản <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={bankTransfer.stk}
                  onChange={(e) => handleBankTransferChange("stk", e.target.value)}
                  className={`w-full px-3 py-2 border ${
                    errors.stk
                      ? "border-red-500 dark:border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  } rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500`}
                  placeholder="****************"
                  disabled={isLoading || isUpdating}
                />
                {errors.stk && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.stk}
                  </p>
                )}
              </div>

              <button
                onClick={handleSavePayment}
                disabled={isLoading || isUpdating || !bankTransfer.accountHolderName.trim() || !bankTransfer.bankName.trim() || !bankTransfer.stk.trim()}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading || isUpdating ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Saving Payment Settings...
                  </>
                ) : (
                  "Save Payment Settings"
                )}
              </button>
            </div>

            <div className="flex items-center mt-4 text-green-600 bg-green-100/50 dark:bg-green-900/20 p-3 rounded">
              <span className="text-yellow-500 mr-2">💡</span>
              <p className="text-sm">Secure international bank transfer option</p>
            </div>
          </div>
        )}
      </div>

      {/* Payout Settings */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Payout Settings
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Current payout configuration and minimum payment threshold.
        </p>

        <div className="space-y-4">
          <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                <DollarSign className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 dark:text-white">
                  Minimum Payout
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Current minimum: $50.00
                </p>
              </div>
              <span className="text-sm text-green-600 dark:text-green-400 font-medium">
                Active
              </span>
            </div>
          </div>

          {/* Payment Status */}
          <div className="space-y-4">
            {/* PayPal Status */}
            <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <CreditCard className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 dark:text-white">
                    PayPal Status
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {paypalEmail || userData?.paypal_email
                      ? `PayPal configured: ${paypalEmail || userData?.paypal_email}`
                      : "PayPal not configured"}
                  </p>
                </div>
                <span
                  className={`text-sm font-medium ${
                    paypalEmail || userData?.paypal_email
                      ? "text-green-600 dark:text-green-400"
                      : "text-gray-500 dark:text-gray-400"
                  }`}
                >
                  {paypalEmail || userData?.paypal_email ? "Ready" : "Not Set"}
                </span>
              </div>
            </div>

            {/* Bank Transfer Status */}
            <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                  <Building2 className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 dark:text-white">
                    Bank Transfer Status
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {userData?.bank_transfer?.stk || userData?.bank_transfer?.account_number
                      ? `STK: ****${(userData.bank_transfer?.stk || userData.bank_transfer?.account_number || '').slice(-4)} - ${userData.bank_transfer?.bank_name || 'Ngân hàng'}`
                      : "Chưa cấu hình chuyển khoản ngân hàng"}
                  </p>
                </div>
                <span
                  className={`text-sm font-medium ${
                    userData?.bank_transfer?.stk || userData?.bank_transfer?.account_number
                      ? "text-green-600 dark:text-green-400"
                      : "text-gray-500 dark:text-gray-400"
                  }`}
                >
                  {userData?.bank_transfer?.stk || userData?.bank_transfer?.account_number ? "Sẵn sàng" : "Chưa thiết lập"}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PayoutSettings;
