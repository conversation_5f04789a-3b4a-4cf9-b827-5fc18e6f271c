import React, { useState, useEffect } from "react";

const HuntAffiliate: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);

  // Using the provided Airtable form embed URL
  const airtableFormUrl = process.env.NEXT_PUBLIC_AIRTABLE_FORM_URL;

  // Force showing the loading indicator for at least 2 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  // Separate the iframe into its own component to ensure proper rendering
   return (
    <div className="w-full h-screen relative">
      {/* Always render the iframe to start loading */}
      <iframe
        className="airtable-embed w-full h-full"
        src={airtableFormUrl}
        title="Submission Form"
        frameBorder="0"
        onWheel={(e) => e.stopPropagation()}
        style={{ opacity: isLoading ? 0 : 1, transition: "opacity 0.5s ease" }}
        onLoad={() => setIsLoading(false)}
      />

      {/* Loading overlay - positioned on top with higher z-index */}
      {isLoading && (
        <div
          className="fixed inset-0 flex items-center justify-center z-50"
          style={{ backgroundColor: "rgba(255, 255, 255, 0.9)" }}
        >
          <div className="flex flex-col items-center p-6 rounded-lg">
            <div className="w-10 h-10 border-4 border-primary-foreground border-t-transparent rounded-full animate-spin mb-4"></div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HuntAffiliate;
