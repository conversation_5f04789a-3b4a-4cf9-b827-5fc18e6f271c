import React, { useState, useEffect } from "react";
import { TopVideos } from "@/containers";
import { TopAds } from "@/containers";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger, TabsContent } from "@/components/ui/tabs";

// Helper function to parse the hash and extract active tab
const getActiveTabFromHash = (): string => {
  if (typeof window === "undefined") return "videos";

  const hash = window.location.hash;
  if (!hash) return "videos";

  const hashParts = hash.substring(1).split("#");
  for (const part of hashParts) {
    if (part.startsWith("tab=")) {
      const tab = part.substring(4);
      if (tab === "videos" || tab === "ads") {
        return tab;
      }
    }
  }

  return "videos"; // Default to videos if no valid tab found
};

// Helper function to update hash when changing tab
const updateHashForTab = (tab: string) => {
  if (typeof window === "undefined") return;

  // Get current hash parameters
  const hash = window.location.hash;
  if (!hash) {
    // If no hash exists, create a default one based on tab
    const defaultSort = tab === "videos" ? "views-desc" : "ctr-desc";
    window.history.replaceState(
      null,
      "",
      `#tab=${tab}#period=1year#sort=${defaultSort}` // Changed from 30days to 1year
    );
    return;
  }

  // Parse existing hash
  const hashParts = hash.substring(1).split("#");
  const updatedParts: string[] = [];

  // Look for tab parameter to update
  let tabUpdated = false;

  for (const part of hashParts) {
    if (part.startsWith("tab=")) {
      // Update tab parameter
      updatedParts.push(`tab=${tab}`);
      tabUpdated = true;
    } else {
      updatedParts.push(part);
    }
  }

  // If no tab parameter was found, add one
  if (!tabUpdated) {
    updatedParts.unshift(`tab=${tab}`);
  }

  // Update URL hash without page reload
  window.history.replaceState(null, "", `#${updatedParts.join("#")}`);
};

export const Top: React.FC = () => {
  // Use state with null initial value to avoid hydration mismatch
  const [activeTab, setActiveTab] = useState<string | null>(null);
  // Track if we're mounted on the client
  const [isMounted, setIsMounted] = useState(false);

  // Initialize component on mount (client-side only)
  useEffect(() => {
    setIsMounted(true);
    setActiveTab(getActiveTabFromHash());
  }, []);

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    updateHashForTab(value);
  };

  // Only render tabs when we're mounted and have an active tab
  if (!isMounted || activeTab === null) {
    // Return a minimal placeholder with the same structure to avoid layout shift
    return <div className="container mx-auto py-6"></div>;
  }

  return (
    <div className="container mx-auto py-6">
      <Tabs
        defaultValue={activeTab}
        onValueChange={handleTabChange}
        value={activeTab}
        className="w-full"
      >
        <TabsList className="mb-6">
          <TabsTrigger value="videos">Top Videos</TabsTrigger>
          <TabsTrigger value="ads">
            Top Ads
          </TabsTrigger>
        </TabsList>
        <TabsContent value="videos">
          <TopVideos />
        </TabsContent>
        <TabsContent value="ads">
          <TopAds />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Top;
