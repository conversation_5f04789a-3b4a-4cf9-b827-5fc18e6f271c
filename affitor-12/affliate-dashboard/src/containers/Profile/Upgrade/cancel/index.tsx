import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import axios from "axios";

export const CancellationContainer = () => {
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>("");

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleKeepSubscription = async () => {
    router.push("/dashboard");
  };

  const handleChooseAnotherPlan = async () => {
    router.push("/profile/upgrade");
  };

  const handleConfirmCancellation = async () => {
    setLoading(true);

    try {
      // Get token from localStorage
      const token =
        typeof window !== "undefined"
          ? localStorage.getItem("auth_token")
          : null;

      // Create headers with token if available
      const headers: Record<string, string> = {};
      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      }

      // Make request to our API endpoint to cancel subscription
      await axios.post(
        "/api/subscription-tiers/cancel-subscription",
        {},
        { headers }
      );

      // Redirect to cancellation confirmation page
      router.push("/profile/upgrade/cancellation-confirmed");
    } catch (error: any) {
      console.error("Error cancelling subscription:", error);
      setErrorMessage(
        error.response?.data?.message ||
          "Failed to cancel your subscription. Please contact support."
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-background/95 p-4">
      <div
        className={`max-w-md w-full transition-all duration-700 ${
          isVisible
            ? "opacity-100 transform translate-y-0"
            : "opacity-0 transform -translate-y-8"
        }`}
      >
        <Card className="border-2 border-amber-100 dark:border-amber-900/50 shadow-lg overflow-hidden">
          <div className="absolute top-0 right-0 w-24 h-24 -mt-8 -mr-8 bg-amber-400 dark:bg-amber-600 rounded-full opacity-20"></div>

          <div className="relative">
            <CardHeader className="pb-2 text-center">
              <div
                className={`flex justify-center mb-6 transition-all duration-1000 delay-300 ${
                  isVisible
                    ? "opacity-100 transform scale-100"
                    : "opacity-0 transform scale-50"
                }`}
              >
                <div className="h-20 w-20 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center">
                  <AlertTriangle className="h-12 w-12 text-amber-600 dark:text-amber-400" />
                </div>
              </div>

              <CardTitle className="text-2xl font-bold text-foreground">
                Cancel Your Subscription?
              </CardTitle>

              <CardDescription className="mt-2">
                We're sorry to see you go. Your benefits will remain active
                until the end of your current billing period.
              </CardDescription>
            </CardHeader>

            <CardContent className="text-center pt-2">
              {errorMessage && (
                <p className="text-red-500 mb-4">{errorMessage}</p>
              )}

              <div className="space-y-2 text-muted-foreground">
                <p>Before you go, would you like to:</p>
              </div>
            </CardContent>

            <CardFooter className="flex flex-col gap-3 pt-2">
              <Button
                className="w-full"
                variant="default"
                onClick={handleChooseAnotherPlan}
              >
                Choose Another Plan
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>

              <Button
                className="w-full"
                variant="outline"
                onClick={handleKeepSubscription}
              >
                Keep My Subscription
              </Button>
            </CardFooter>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default CancellationContainer;
