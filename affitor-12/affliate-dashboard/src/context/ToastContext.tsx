import React, { createContext, useContext, useState, ReactNode } from 'react';
import { 
  Toast, 
  ToastClose, 
  ToastDescription, 
  ToastProvider, 
  ToastTitle, 
  ToastViewport 
} from '@/components/ui/toast';

type ToastType = 'default' | 'success' | 'destructive' | 'warning';

type ToastData = {
  id: string;
  title: string;
  description?: string;
  type: ToastType;
  duration?: number;
  action?: ReactNode;
};

interface ToastContextType {
  toasts: ToastData[];
  showToast: (title: string, description?: string, type?: ToastType, duration?: number, action?: ReactNode) => void;
  dismissToast: (id: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export const ToastContextProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastData[]>([]);

  const showToast = (
    title: string, 
    description?: string, 
    type: ToastType = 'default', 
    duration = 5000,
    action?: ReactNode
  ) => {
    const id = Math.random().toString(36).substring(2, 9);
    
    setToasts((prev) => [...prev, { id, title, description, type, duration, action }]);
    
    // Auto dismiss
    if (duration !== Infinity) {
      setTimeout(() => {
        dismissToast(id);
      }, duration);
    }
    
    return id;
  };

  const dismissToast = (id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  };

  return (
    <ToastContext.Provider value={{ toasts, showToast, dismissToast }}>
      <ToastProvider>
        {children}
        
        {toasts.map(({ id, title, description, type, action }) => (
          <Toast key={id} variant={type}>
            <div className="grid gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && <ToastDescription>{description}</ToastDescription>}
            </div>
            {action}
            <ToastClose onClick={() => dismissToast(id)} />
          </Toast>
        ))}
        
        <ToastViewport />
      </ToastProvider>
    </ToastContext.Provider>
  );
};
