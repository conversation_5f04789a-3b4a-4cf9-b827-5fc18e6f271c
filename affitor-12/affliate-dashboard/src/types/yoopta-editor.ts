/**
 * Yoopta Editor TypeScript Definitions for Frontend
 *
 * Re-export types from the official Yoopta Editor package
 * and add custom extensions for our application.
 */

// Re-export official Yoopta Editor types
export type {
  YooptaContentValue,
  YooptaBlock,
} from '@yoopta/editor';

// Custom block types for our application
export type CustomYooptaBlockType =
  | 'paragraph'
  | 'heading-one'
  | 'heading-two'
  | 'heading-three'
  | 'heading-four'
  | 'heading-five'
  | 'heading-six'
  | 'blockquote'
  | 'bulleted-list'
  | 'numbered-list'
  | 'code'
  | 'divider'
  | 'image'
  | 'video'
  | 'link'
  | 'file'
  | 'callout'
  | 'table'
  | 'accordion'
  | 'embed';

/**
 * Page content structure with Yoopta Editor integration
 */
export interface PageContent {
  /** Rich text content in Yoopta Editor format */
  content?: any;
  /** HTML representation for SEO and fallback rendering */
  content_html?: string;
  /** Plain text representation for search and excerpts */
  content_plain?: string;
}

/**
 * Editor configuration types
 */
export interface YooptaEditorConfig {
  autoSave?: boolean;
  autoSaveInterval?: number;
  placeholder?: string;
  readOnly?: boolean;
  allowedBlocks?: CustomYooptaBlockType[];
  maxBlocks?: number;
  enabledFeatures?: {
    toolbar?: boolean;
    shortcuts?: boolean;
    dragAndDrop?: boolean;
    blockSelection?: boolean;
  };
}

/**
 * Content statistics
 */
export interface ContentStats {
  blockCount: number;
  wordCount: number;
  characterCount: number;
  readingTime: number;
  blockTypes: Record<CustomYooptaBlockType, number>;
}

/**
 * Content processing utilities
 */
export interface ContentProcessingResult {
  html: string;
  plainText: string;
  wordCount: number;
  readingTime: number; // in minutes
}

/**
 * Content validation result
 */
export interface ContentValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Export/Import formats
 */
export interface YooptaExportOptions {
  format: 'html' | 'markdown' | 'plaintext' | 'json';
  includeMetadata?: boolean;
  preserveFormatting?: boolean;
}

export interface YooptaImportOptions {
  format: 'html' | 'markdown' | 'plaintext';
  preserveStructure?: boolean;
  generateIds?: boolean;
}
