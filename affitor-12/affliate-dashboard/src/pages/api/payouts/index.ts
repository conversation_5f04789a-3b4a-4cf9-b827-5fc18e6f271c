import { NextApiRequest, NextApiResponse } from "next";

const STRAPI_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method === "GET") {
    try {
      const { authorization } = req.headers;
      const { type, ...queryParams } = req.query;

      if (!authorization) {
        return res.status(401).json({ error: "Authorization token required" });
      }

      // Build query string for pagination and filtering
      const searchParams = new URLSearchParams();
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value) {
          searchParams.append(key, String(value));
        }
      });
      const queryString = searchParams.toString();

      // Determine endpoint based on type parameter
      let endpoint = "/api/payouts";
      if (type === "overview") {
        endpoint = "/api/payouts/overview";
      } else if (queryString) {
        endpoint = `/api/payouts?${queryString}`;
      }

      // Forward the request to Strapi
      const response = await fetch(`${STRAPI_URL}${endpoint}`, {
        method: "GET",
        headers: {
          Authorization: authorization,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        return res.status(response.status).json(errorData);
      }

      const data = await response.json();
      return res.status(200).json(data);
    } catch (error) {
      console.error("Payout API error:", error);
      return res.status(500).json({ error: "Internal server error" });
    }
  } else {
    res.setHeader("Allow", ["GET"]);
    return res.status(405).json({ error: `Method ${req.method} not allowed` });
  }
}
