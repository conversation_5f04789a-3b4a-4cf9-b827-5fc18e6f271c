import { NextApiRequest, NextApiResponse } from 'next';
import { createApiContext } from '@/utils/api-middleware';
import { sendApiError } from '@/utils/api-error-handler';
import { StrapiClient } from '@/utils/request';

interface UserPrompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  tags?: string[];
  isFavorite: boolean;
  usageCount: number;
  lastUsedAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface RecentPromptsResponse {
  data: UserPrompt[];
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<RecentPromptsResponse | any>
) {
  try {
    const { token } = createApiContext(req, { requireAuth: true });

    if (req.method === 'GET') {
      // Get recent prompts
      const response: any = await StrapiClient.client.get('/api/user-prompts/recent', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return res.status(200).json(response.data);
    }

    // Method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error: any) {
    console.error('Error in recent prompts API route:', error);
    sendApiError(res, error, 'Error fetching recent prompts');
  }
}
