import { NextApiRequest, NextApiResponse } from 'next';
import { createApiContext } from '@/utils/api-middleware';
import { sendApiError } from '@/utils/api-error-handler';
import { StrapiClient } from '@/utils/request';

interface TagsResponse {
  data: string[];
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<TagsResponse | any>
) {
  try {
    const { token } = createApiContext(req, { requireAuth: true });

    if (req.method === 'GET') {
      // Get user tags
      const response: any = await StrapiClient.client.get('/api/user-prompts/tags', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return res.status(200).json(response.data);
    }

    // Method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error: any) {
    console.error('Error in user tags API route:', error);
    sendApiError(res, error, 'Error fetching user tags');
  }
}
