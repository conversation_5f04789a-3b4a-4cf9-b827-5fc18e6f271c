import { NextApiRequest, NextApiResponse } from "next";
import { AppError } from "@/interfaces";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";
import { StrapiAdminClient } from "@/utils/request";

interface PaymentInfo {
  paypal_email?: string;
  bank_transfer?: {
    // Vietnamese banking fields (new format)
    account_holder_name?: string; // Vietnamese banking standard - Chủ tài khoản
    bank_name?: string; // Vietnamese banking standard - Tên ngân hàng
    stk?: string; // Vietnamese banking standard - STK (Số tài khoản)
    // International banking fields (legacy format for backward compatibility)
  };
}

interface PaymentInfoResponse {
  partnerId: string;
  paymentInfo: PaymentInfo;
  hasPaymentInfo: boolean;
  availableMethods: string[];
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<PaymentInfoResponse | AppError>
) {
  if (req.method !== "GET") {
    return res.status(405).json({ 
      statusCode: 405, 
      message: "Method not allowed" 
    });
  }

  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });
    const { id: partnerId } = req.query;

    if (!partnerId || typeof partnerId !== "string") {
      return res.status(400).json({
        statusCode: 400,
        message: "Invalid partner ID",
      });
    }

    // Fetch partner details with user information including payment data
    const partnerResponse: any = await StrapiAdminClient.client.get(
      `/content-manager/collection-types/api::referrer.referrer/${partnerId}?populate[user]=paypal_email,bank_transfer`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const partner = partnerResponse.data;
    
    if (!partner || !partner.user) {
      return res.status(404).json({
        statusCode: 404,
        message: "Partner not found or user data unavailable",
      });
    }

    const user = partner.user;
    const paymentInfo: PaymentInfo = {};
    const availableMethods: string[] = [];

    // Extract PayPal information
    if (user.paypal_email) {
      paymentInfo.paypal_email = user.paypal_email;
      availableMethods.push("paypal");
    }

    // Extract bank transfer information
    if (user.bank_transfer) {
      paymentInfo.bank_transfer = user.bank_transfer;

      // Check if bank transfer has the required information
      // For Vietnamese format: account_holder_name, bank_name, stk
      // For international format: first_name + last_name (or business_name), account_number
      const hasVietnameseFormat = user.bank_transfer.account_holder_name &&
                                  user.bank_transfer.bank_name &&
                                  user.bank_transfer.stk;
      const hasInternationalFormat = user.bank_transfer.account_number &&
                                     (user.bank_transfer.business_name ||
                                      (user.bank_transfer.first_name && user.bank_transfer.last_name));



      if (hasVietnameseFormat || hasInternationalFormat) {
        availableMethods.push("bank transfer");
      }
    }

    const hasPaymentInfo = availableMethods.length > 0;

    const response: PaymentInfoResponse = {
      partnerId: partnerId,
      paymentInfo,
      hasPaymentInfo,
      availableMethods,
    };

    res.status(200).json(response);
  } catch (error: any) {
    console.error("Error fetching partner payment info:", error);
    sendApiError(res, error, "Error fetching partner payment information");
  }
}
