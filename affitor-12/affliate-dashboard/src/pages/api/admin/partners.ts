import { NextApiRequest, NextApiResponse } from "next";
import { StrapiAdminClient } from "@/utils/request";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    // Get admin token from headers
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({ error: "Admin authentication required" });
    }

    const token = authHeader.replace("Bearer ", "");
    const { method, query } = req;

    if (method === "GET") {
      const {
        page = 1,
        pageSize = 10,
        search = "",
        status,
        sort,
        username,
        email,
        createdAtFrom,
        createdAtTo,
        revenueFrom,
        revenueTo,
        commissionFrom,
        commissionTo,
        clicksFrom,
        clicksTo,
        leadsFrom,
        leadsTo,
        conversionsFrom,
        conversionsTo
      } = query;

      const response = await StrapiAdminClient.getPartners(
        {
          page: Number(page),
          pageSize: Number(pageSize),
          search: search.toString(),
          status: status ? status.toString() : undefined,
          sort: sort ? sort.toString() : undefined,
          username: username ? username.toString() : undefined,
          email: email ? email.toString() : undefined,
          createdAtFrom: createdAtFrom ? createdAtFrom.toString() : undefined,
          createdAtTo: createdAtTo ? createdAtTo.toString() : undefined,
          revenueFrom: revenueFrom ? revenueFrom.toString() : undefined,
          revenueTo: revenueTo ? revenueTo.toString() : undefined,
          commissionFrom: commissionFrom ? commissionFrom.toString() : undefined,
          commissionTo: commissionTo ? commissionTo.toString() : undefined,
          clicksFrom: clicksFrom ? clicksFrom.toString() : undefined,
          clicksTo: clicksTo ? clicksTo.toString() : undefined,
          leadsFrom: leadsFrom ? leadsFrom.toString() : undefined,
          leadsTo: leadsTo ? leadsTo.toString() : undefined,
          conversionsFrom: conversionsFrom ? conversionsFrom.toString() : undefined,
          conversionsTo: conversionsTo ? conversionsTo.toString() : undefined,
        },
        token
      );

      // Transform the response to match expected format
      const responseData = response as any;

      // Strapi content manager returns data directly in results array
      const partnersData = responseData.data?.results || responseData.results || [];
      const paginationData = responseData.data?.pagination || responseData.pagination || {};

      const transformedData = {
        results: partnersData,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          pageCount: paginationData.pageCount || 0,
          total: paginationData.total || 0,
        },
      };

      res.status(200).json(transformedData);
    } else if (method === "PUT") {
      // Update partner status
      const { partnerId } = query;
      const { status } = req.body;

      if (!partnerId || !status) {
        return res.status(400).json({
          error: "Partner ID and status are required",
        });
      }

      const data = await StrapiAdminClient.updatePartnerStatus(
        partnerId.toString(),
        status,
        token
      );

      res.status(200).json(data);
    } else if (method === "DELETE") {
      // Delete partner
      const { partnerId } = query;

      if (!partnerId) {
        return res.status(400).json({ error: "Partner ID is required" });
      }

      await StrapiAdminClient.deletePartner(partnerId.toString(), token);

      res.status(200).json({ success: true });
    } else {
      res.status(405).json({ error: "Method not allowed" });
    }
  } catch (error: any) {
    console.error("Admin partners API error:", error);

    // Handle different error types
    if (error.statusCode) {
      return res.status(error.statusCode).json({
        error: error.message || "Operation failed",
      });
    }

    res.status(500).json({ error: "Internal server error" });
  }
}
