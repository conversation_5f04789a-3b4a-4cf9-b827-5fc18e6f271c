import { NextApiRequest, NextApiResponse } from "next";
import { StrapiAdminClient } from "@/utils/request";

// Helper function to format time ago
function formatTimeAgo(dateString: string): string {
  const now = new Date();
  const date = new Date(dateString);
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return `${diffInSeconds} sec ago`;
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} min ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? "s" : ""} ago`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? "s" : ""} ago`;
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Get admin token from headers
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({ error: "Admin authentication required" });
    }

    const token = authHeader.replace("Bearer ", "");

    // Get dashboard stats
    const data: any = await StrapiAdminClient.getDashboardStats(token);

    // Get recent customers (referrals) sorted by creation date
    const recentCustomersData: any = await StrapiAdminClient.getReferrals(
      {
        page: 1,
        pageSize: 10,
        sort: "createdAt:DESC", // Sort by creation date, newest first
      },
      token
    );

    // Transform the data to match UI expectations
    const transformedData = {
      success: true,
      data: {
        totalReferrers: data.data?.totalReferrers || 0,
        totalReferrals: data.data?.totalReferrals || 0,
        totalRevenue: data.data?.totalRevenue || 0,
        totalClicks: data.data?.totalClicks || 0,
        totalLeads: data.data?.totalLeads || 0,
        currentMonthRevenue: data.data?.currentMonthRevenue || 0,
        previousMonthRevenueAmount: data.data?.previousMonthRevenueAmount || 0,
        growthRate: data.data?.growthRate || 0,
        monthlyPerformance: data.data?.monthlyPerformance || [],
        recentActivity: (data.data?.recentActivity || []).map(
          (activity: any) => ({
            id: activity.id,
            documentId: activity.documentId,
            amount_paid: activity.amount_paid,
            referral_status: activity.referral_status,
            description: activity.description,
            createdAt: activity.createdAt,
            updatedAt: activity.updatedAt,
            publishedAt: activity.publishedAt,
            locale: activity.locale,
            referral: activity.referral
          })
        ),
        recentCustomers: (recentCustomersData.results || []).map(
          (customer: any) => ({
            id: customer.id,
            documentId: customer.documentId,
            referral_status: customer.referral_status,
            total_paid: customer.total_paid || 0,
            total_commission: customer.total_commission || 0,
            createdAt: customer.createdAt,
            updatedAt: customer.updatedAt,
            publishedAt: customer.publishedAt,
            user: customer.user ? {
              id: customer.user.id,
              documentId: customer.user.documentId,
              username: customer.user.username,
              email: customer.user.email,
              first_name: customer.user.first_name,
              last_name: customer.user.last_name,
            } : null,
            referrer: customer.referrer ? {
              id: customer.referrer.id,
              documentId: customer.referrer.documentId,
              referral_code: customer.referrer.referral_code,
              user: customer.referrer.user ? {
                first_name: customer.referrer.user.first_name,
                last_name: customer.referrer.user.last_name,
                email: customer.referrer.user.email,
                username: customer.referrer.user.username,
              } : null,
            } : null,
          })
        ),
        topReferrers: (data.data?.topReferrers || []).map(
          (referrer: any) => ({
            id: referrer.id,
            documentId: referrer.documentId,
            referral_code: referrer.referral_code,
            referrer_status: referrer.referrer_status,
            createdAt: referrer.createdAt,
            updatedAt: referrer.updatedAt,
            publishedAt: referrer.publishedAt,
            locale: referrer.locale,
            balance: referrer.balance,
            total_earnings: referrer.total_earnings,
            total_revenue: referrer.total_revenue,
            user: referrer.user,
            totalClicks: referrer.totalClicks,
            totalLeads: referrer.totalLeads,
            totalConversions: referrer.totalConversions,
            totalCustomers: referrer.totalCustomers,
            totalEarnings: referrer.totalEarnings,
            totalRevenue: referrer.totalRevenue,
            commissionStats: referrer.commissionStats,
          })
        ),
        topReferrals: (data.data?.topReferrals || []).map(
          (referral: any) => ({
            id: referral.id,
            documentId: referral.documentId,
            referral_status: referral.referral_status,
            total_paid: referral.total_paid,
            createdAt: referral.createdAt,
            updatedAt: referral.updatedAt,
            publishedAt: referral.publishedAt,
            locale: referral.locale,
            user: referral.user,
          })
        ),
      },
    };

    console.log('transformedData:', transformedData);
    res.status(200).json(transformedData);
  } catch (error: any) {
    console.error("Admin dashboard API error:", error);

    // Return error response instead of mock data
    const errorMessage = error.message || "Failed to fetch dashboard data";
    const statusCode = error.statusCode || 500;

    return res.status(statusCode).json({
      success: false,
      error: errorMessage,
      data: {
        totalReferrers: 0,
        totalReferrals: 0,
        totalRevenue: 0,
        totalClicks: 0,
        totalLeads: 0,
        currentMonthRevenue: 0,
        previousMonthRevenueAmount: 0,
        growthRate: 0,
        monthlyPerformance: [],
        recentActivity: [],
        recentCustomers: [],
        topReferrers: [],
        topReferrals: [],
      },
    });
  }
}
