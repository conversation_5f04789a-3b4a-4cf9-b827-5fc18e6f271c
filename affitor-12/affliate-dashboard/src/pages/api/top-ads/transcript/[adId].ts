import { AppError } from "@/interfaces";
import { StrapiClient } from "@/utils/request";
import type { NextApiRequest, NextApiResponse } from "next";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ transcript: string } | AppError>
) {
  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    // Extract adId from query
    const { adId } = req.query;

    if (!adId || typeof adId !== "string") {
      return sendApiError(
        res,
        {
          statusCode: 400,
          message: "Invalid ad ID",
        },
        "Invalid ad ID"
      );
    }

    // Forward the token to the backend request
    const response = await StrapiClient.getAdTranscript(adId, token);
    console.log("Ad Transcript API response:", response);
    res.status(200).json(response);
  } catch (error: any) {
    console.error("Ad Transcript API error:", error);
    sendApiError(res, error, "Error fetching ad transcript");
  }
}
