import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { toast } from 'sonner';

export default function DiscourseCallback() {
  const router = useRouter();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const { status: queryStatus, message: queryMessage } = router.query;

    if (queryStatus) {
      setStatus(queryStatus as 'success' | 'error');
      setMessage(queryMessage as string || '');

      if (queryStatus === 'success') {
        toast.success('Successfully connected to community!');
        // Redirect to home page after a short delay
        setTimeout(() => {
          router.push('/');
        }, 2000);
      } else if (queryStatus === 'error') {
        toast.error(queryMessage as string || 'Failed to connect to community');
        // Redirect to home page after a short delay
        setTimeout(() => {
          router.push('/');
        }, 3000);
      }
    }
  }, [router.query, router]);

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>;
      case 'success':
        return <div className="text-green-600 text-6xl">✓</div>;
      case 'error':
        return <div className="text-red-600 text-6xl">✗</div>;
      default:
        return null;
    }
  };

  const getStatusMessage = () => {
    switch (status) {
      case 'loading':
        return 'Processing community connection...';
      case 'success':
        return 'Successfully connected to community!';
      case 'error':
        return message || 'Failed to connect to community';
      default:
        return '';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'loading':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            {getStatusIcon()}
          </div>
          
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
            Community Connection
          </h2>
          
          <p className={`mt-2 text-sm ${getStatusColor()}`}>
            {getStatusMessage()}
          </p>

          {status === 'success' && (
            <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <p className="text-sm text-green-700 dark:text-green-300">
                You can now close this window or you'll be redirected automatically.
              </p>
            </div>
          )}

          {status === 'error' && (
            <div className="mt-4 space-y-4">
              <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <p className="text-sm text-red-700 dark:text-red-300">
                  {message || 'There was an error connecting to the community. Please try again.'}
                </p>
              </div>
              
              <button
                onClick={() => router.push('/')}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Return to Home
              </button>
            </div>
          )}

          {status === 'loading' && (
            <div className="mt-4">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Please wait while we connect you to the community...
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
