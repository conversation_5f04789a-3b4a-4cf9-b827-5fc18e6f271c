# ECR Image configuration
ecr_image = "505993055293.dkr.ecr.eu-central-1.amazonaws.com/fight-tv:dev"
bucket_name    = "fight-tv-ecs-service-app-configuration"

# AWS Region
aws_region = "eu-central-1"

# VPC and Network settings
vpc_cidr = "********/16"
public_subnets = ["********/24", "********/24"]
private_subnets = ["********/24", "********/24"]

# ECS settings
ecs_cluster_name = "fight-tv-dev-cluster"
ecs_service_name = "fight-tv-dev-service"
container_port = 80
desired_count = 1
cpu = 256
memory = 512

# Load balancer settings
lb_name = "fight-tv-dev-lb"
lb_internal = false
lb_listener_port = 80
lb_health_check_path = "/health"

# Tags
environment = "dev"

# RDS access
ip_address = "************"