#!/bin/bash

set -e

# --- Config ---
AWS_REGION="eu-central-1"
REPO_NAME="fight-tv"
IMAGE_TAG="dev"

# --- Get AWS Account ID ---
ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

# --- Construct ECR Registry URL ---
ECR_REGISTRY="$ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com"
FULL_IMAGE="$ECR_REGISTRY/$REPO_NAME:$IMAGE_TAG"

# --- Login to ECR ---
echo "🔐 Logging in to ECR..."
aws ecr get-login-password --region "$AWS_REGION" | docker login --username AWS --password-stdin "$ECR_REGISTRY"

# --- Enable BuildKit (if not already) ---
export DOCKER_BUILDKIT=1

# --- Build Docker Image for Fargate (x86_64 = linux/amd64) ---
echo "🐳 Building Docker image for Fargate (linux/amd64): $FULL_IMAGE ..."
docker buildx build \
  --platform linux/amd64 \
  -t "$FULL_IMAGE" \
  -f Dockerfile.dev \
  .

# --- Push to ECR ---
echo "📤 Pushing image to ECR..."
docker push "$FULL_IMAGE"

echo "✅ Image successfully pushed: $FULL_IMAGE"
