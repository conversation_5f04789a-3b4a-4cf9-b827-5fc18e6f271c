export default {
  /**
   * Validates JWT token and returns user information
   */
  validateToken: async (ctx) => {
    try {
      const { authorization } = ctx.request.header;

      if (!authorization) {
        return ctx.badRequest('Authorization header is missing');
      }

      // Extract the token from the Authorization header
      const token = authorization.split(' ')[1];

      if (!token) {
        return ctx.badRequest('Token is missing');
      }

      // Verify and decode the token
      const { id, exp } = await strapi.plugins['users-permissions'].services.jwt.verify(token);

      // Get the user from the database
      const user = await strapi.query('plugin::users-permissions.user').findOne({ where: { id } });

      if (!user) {
        return ctx.notFound('User not found');
      }

      // Manually sanitize the user object by excluding sensitive information
      const { password, resetPasswordToken, confirmationToken, ...sanitizedUser } = user;

      return {
        user: sanitizedUser,
        expiresAt: new Date(exp * 1000).toISOString(),
      };
    } catch (error) {
      console.error('Error validating token:', error);
      if (error.message === 'jwt expired') {
        return ctx.unauthorized('Token expired');
      }
      return ctx.badRequest('Invalid token');
    }
  },

  /**
   * Get Firebase authentication URL for Google Sign-in
   */
  getFirebaseGoogleAuthUrl: async (ctx) => {
    const firebaseAuthService = strapi.service('api::auth.firebase-auth');
    const authUrl = firebaseAuthService.getGoogleAuthUrl();
    ctx.body = { googleAuthUrl: authUrl };
  },

  /**
   * Authenticate with Firebase token (from any provider)
   */
  firebaseAuthenticate: async (ctx) => {
    const { idToken } = ctx.request.body;

    if (!idToken) {
      return ctx.badRequest('Firebase ID token is required');
    }

    try {
      const firebaseAuthService = strapi.service('api::auth.firebase-auth');
      // Pass the context to the service for cookie access
      const { jwt, user } = await firebaseAuthService.authenticate(idToken, ctx);

      return {
        jwt,
        user,
      };
    } catch (error) {
      console.error('Firebase authentication error:', error);
      return ctx.badRequest(error.message);
    }
  },

  /**
   * Handle redirect after Firebase Google authentication (client-side flow)
   */
  firebaseGoogleCallback: async (ctx) => {
    const { idToken } = ctx.request.body;

    if (!idToken) {
      return ctx.badRequest('Firebase ID token is required');
    }

    try {
      const firebaseAuthService = strapi.service('api::auth.firebase-auth');
      const { jwt, user } = await firebaseAuthService.authenticate(idToken);

      return {
        jwt,
        user,
      };
    } catch (error) {
      console.error('Firebase Google authentication error:', error);
      return ctx.badRequest(error.message);
    }
  },
};
